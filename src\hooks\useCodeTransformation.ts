
import { useState, useCallback } from 'react';
import { AIService } from '../services/aiService';

interface LogEntry {
  id: number;
  agent: 'planner' | 'critic' | 'system';
  message: string;
  timestamp: Date;
}

interface UseCodeTransformationReturn {
  isRunning: boolean;
  logs: LogEntry[];
  diffContent: string;
  transformedCode: string;
  runTransformation: (code: string) => Promise<void>;
  stopTransformation: () => void;
  clearLogs: () => void;
  progress: number;
}

export const useCodeTransformation = (): UseCodeTransformationReturn => {
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState<LogEntry[]>([
    { id: 1, agent: 'system', message: 'Metamorphic Reactor initialized', timestamp: new Date() }
  ]);
  const [diffContent, setDiffContent] = useState('');
  const [transformedCode, setTransformedCode] = useState('');
  const [progress, setProgress] = useState(0);
  const [aiService] = useState(() => new AIService());

  const addLog = useCallback((entry: Omit<LogEntry, 'id'>) => {
    setLogs(prev => [...prev, { ...entry, id: Date.now() + Math.random() }]);
  }, []);

  const runTransformation = useCallback(async (code: string) => {
    if (isRunning) return;
    
    setIsRunning(true);
    setDiffContent('');
    setTransformedCode('');
    setProgress(0);
    
    try {
      const result = await aiService.transformCode(code, (entry) => {
        addLog(entry);
        // Update progress based on log messages
        if (entry.message.includes('Iteration 1')) setProgress(20);
        else if (entry.message.includes('Iteration 2')) setProgress(40);
        else if (entry.message.includes('Iteration 3')) setProgress(60);
        else if (entry.message.includes('Iteration 4')) setProgress(80);
        else if (entry.message.includes('completed')) setProgress(100);
      });
      
      setDiffContent(result.diff);
      setTransformedCode(result.transformedCode);
      
      addLog({
        agent: 'system',
        message: `✨ Transformation completed in ${result.iterations} iteration(s). Final quality score: ${result.finalScore.toFixed(2)}/1.00`,
        timestamp: new Date()
      });
      
      setProgress(100);
    } catch (error) {
      addLog({
        agent: 'system',
        message: `❌ Error during transformation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      });
      setProgress(0);
    } finally {
      setIsRunning(false);
    }
  }, [isRunning, aiService, addLog]);

  const stopTransformation = useCallback(() => {
    setIsRunning(false);
    setProgress(0);
    addLog({
      agent: 'system',
      message: '⏹️ Transformation stopped by user',
      timestamp: new Date()
    });
  }, [addLog]);

  const clearLogs = useCallback(() => {
    setLogs([
      { id: 1, agent: 'system', message: 'Metamorphic Reactor initialized', timestamp: new Date() }
    ]);
    setDiffContent('');
    setTransformedCode('');
    setProgress(0);
  }, []);

  return {
    isRunning,
    logs,
    diffContent,
    transformedCode,
    runTransformation,
    stopTransformation,
    clearLogs,
    progress
  };
};
