import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
      })),
    })),
    upsert: jest.fn(),
    delete: jest.fn(() => ({
      eq: jest.fn(),
    })),
  })),
  rpc: jest.fn(),
};

jest.mock('../lib/supabase', () => ({
  supabase: mockSupabase,
}));

// Mock fetch
global.fetch = jest.fn();

describe('GitHub Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('OAuth Flow', () => {
    it('should generate correct OAuth URL', () => {
      const generateOAuthUrl = (clientId: string, redirectUri: string): string => {
        const scopes = 'repo,user:email';
        const state = 'test-state';
        return `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&state=${state}`;
      };

      const url = generateOAuthUrl('test-client-id', 'http://localhost:3000/auth/github/callback');
      
      expect(url).toContain('https://github.com/login/oauth/authorize');
      expect(url).toContain('client_id=test-client-id');
      expect(url).toContain('scope=repo%2Cuser%3Aemail');
      expect(url).toContain('redirect_uri=http%3A//localhost%3A3000/auth/github/callback');
    });

    it('should exchange code for access token', async () => {
      const mockTokenResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          access_token: 'gho_test_token',
          token_type: 'bearer',
          scope: 'repo,user:email',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockTokenResponse as any);

      const exchangeCodeForToken = async (code: string) => {
        const response = await fetch('https://github.com/login/oauth/access_token', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            client_id: 'test-client-id',
            client_secret: 'test-client-secret',
            code,
          }),
        });

        return response.json();
      };

      const result = await exchangeCodeForToken('test-code');
      
      expect(result.access_token).toBe('gho_test_token');
      expect(result.token_type).toBe('bearer');
      expect(fetch).toHaveBeenCalledWith(
        'https://github.com/login/oauth/access_token',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Accept': 'application/json',
          }),
        })
      );
    });

    it('should handle OAuth errors', async () => {
      const mockErrorResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          error: 'invalid_grant',
          error_description: 'The provided authorization grant is invalid',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockErrorResponse as any);

      const exchangeCodeForToken = async (code: string) => {
        const response = await fetch('https://github.com/login/oauth/access_token', {
          method: 'POST',
          body: JSON.stringify({ code }),
        });

        const data = await response.json();
        if (data.error) {
          throw new Error(data.error_description || data.error);
        }
        return data;
      };

      await expect(exchangeCodeForToken('invalid-code')).rejects.toThrow('The provided authorization grant is invalid');
    });
  });

  describe('Token Management', () => {
    it('should store encrypted tokens in Supabase', async () => {
      const mockUser = { id: 'user-123' };
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: mockUser } });
      mockSupabase.rpc.mockResolvedValue({ data: 'encrypted-token' });
      mockSupabase.from().upsert.mockResolvedValue({ error: null });

      const storeToken = async (accessToken: string, userInfo: any) => {
        const { data: { user } } = await mockSupabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');

        const { data: encryptedToken } = await mockSupabase.rpc('encrypt_secret', {
          secret: accessToken
        });

        await mockSupabase.from('github_tokens').upsert({
          user_id: user.id,
          access_token_encrypted: encryptedToken,
          github_user_id: userInfo.id,
          github_username: userInfo.login,
        });
      };

      await storeToken('gho_test_token', { id: 123, login: 'testuser' });

      expect(mockSupabase.rpc).toHaveBeenCalledWith('encrypt_secret', {
        secret: 'gho_test_token'
      });
      expect(mockSupabase.from).toHaveBeenCalledWith('github_tokens');
    });

    it('should retrieve and decrypt stored tokens', async () => {
      const mockUser = { id: 'user-123' };
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: mockUser } });
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { access_token_encrypted: 'encrypted-token' },
        error: null,
      });
      mockSupabase.rpc.mockResolvedValue({ data: 'decrypted-token' });

      const getStoredToken = async () => {
        const { data: { user } } = await mockSupabase.auth.getUser();
        if (!user) return null;

        const { data } = await mockSupabase
          .from('github_tokens')
          .select('access_token_encrypted')
          .eq('user_id', user.id)
          .single();

        if (!data?.access_token_encrypted) return null;

        const { data: decryptedToken } = await mockSupabase.rpc('decrypt_secret', {
          encrypted_secret: data.access_token_encrypted
        });

        return decryptedToken;
      };

      const token = await getStoredToken();
      expect(token).toBe('decrypted-token');
    });
  });

  describe('GitHub API Calls', () => {
    it('should get current user info', async () => {
      const mockUserResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          id: 123,
          login: 'testuser',
          name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://github.com/avatar.jpg',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockUserResponse as any);

      const getCurrentUser = async (token: string) => {
        const response = await fetch('https://api.github.com/user', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
          },
        });

        return response.json();
      };

      const user = await getCurrentUser('test-token');
      
      expect(user.login).toBe('testuser');
      expect(user.id).toBe(123);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.github.com/user',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
          }),
        })
      );
    });

    it('should get user repositories', async () => {
      const mockReposResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue([
          {
            id: 1,
            name: 'test-repo',
            full_name: 'testuser/test-repo',
            owner: { login: 'testuser' },
            default_branch: 'main',
            permissions: { admin: true, push: true, pull: true },
          },
        ]),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockReposResponse as any);

      const getUserRepos = async (token: string) => {
        const response = await fetch('https://api.github.com/user/repos?sort=updated&per_page=100', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
          },
        });

        const repos = await response.json();
        return repos.filter((repo: any) => repo.permissions.push);
      };

      const repos = await getUserRepos('test-token');
      
      expect(repos).toHaveLength(1);
      expect(repos[0].name).toBe('test-repo');
      expect(repos[0].permissions.push).toBe(true);
    });
  });

  describe('Pull Request Creation', () => {
    it('should create a branch', async () => {
      const mockBranchResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          ref: 'refs/heads/reactor/test-branch',
          object: { sha: 'abc123' },
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>)
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({
            object: { sha: 'base-sha' },
          }),
        } as any)
        .mockResolvedValueOnce(mockBranchResponse as any);

      const createBranch = async (owner: string, repo: string, branchName: string, baseBranch: string = 'main') => {
        // Get base branch SHA
        const baseRef = await fetch(`https://api.github.com/repos/${owner}/${repo}/git/ref/heads/${baseBranch}`);
        const baseData = await baseRef.json();
        
        // Create new branch
        const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/git/refs`, {
          method: 'POST',
          body: JSON.stringify({
            ref: `refs/heads/${branchName}`,
            sha: baseData.object.sha,
          }),
        });

        return response.json();
      };

      const result = await createBranch('testuser', 'test-repo', 'reactor/test-branch');
      
      expect(result.ref).toBe('refs/heads/reactor/test-branch');
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should create a pull request', async () => {
      const mockPRResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          number: 123,
          html_url: 'https://github.com/testuser/test-repo/pull/123',
          url: 'https://api.github.com/repos/testuser/test-repo/pulls/123',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockPRResponse as any);

      const createPullRequest = async (owner: string, repo: string, title: string, body: string, head: string, base: string) => {
        const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/pulls`, {
          method: 'POST',
          body: JSON.stringify({
            title,
            body,
            head,
            base,
            draft: true,
          }),
        });

        return response.json();
      };

      const pr = await createPullRequest(
        'testuser',
        'test-repo',
        'Test PR',
        'Test description',
        'reactor/test-branch',
        'main'
      );

      expect(pr.number).toBe(123);
      expect(pr.html_url).toContain('/pull/123');
    });
  });

  describe('Patch Application', () => {
    it('should apply simple patch operations', () => {
      const applyPatchToContent = (content: string, patch: any): string => {
        let result = content;

        if (patch.operations) {
          for (const op of patch.operations) {
            switch (op.op) {
              case 'add':
                if (op.path === '/end') {
                  result += `\n${op.value}`;
                }
                break;
              case 'replace':
                if (op.path === '/content') {
                  result = op.value;
                }
                break;
            }
          }
        }

        return result;
      };

      const originalContent = 'function test() {\n  console.log("original");\n}';
      const patch = {
        operations: [
          { op: 'add', path: '/end', value: '\n// Added by reactor' },
        ],
      };

      const result = applyPatchToContent(originalContent, patch);
      
      expect(result).toContain('original');
      expect(result).toContain('// Added by reactor');
    });

    it('should generate meaningful commit messages', () => {
      const generateCommitMessage = (patch: any, options: any = {}) => {
        const title = options.title || patch.description || 'Apply code transformation';
        const confidence = patch.confidence ? (patch.confidence * 100).toFixed(1) + '%' : 'N/A';
        const operations = patch.operations?.map((op: any, i: number) => 
          `${i + 1}. ${op.op.toUpperCase()} ${op.path}`
        ).join('\n') || 'No operations';

        return `feat: ${title}

Applied transformation with confidence: ${confidence}

Operations:
${operations}`;
      };

      const patch = {
        description: 'Add error handling',
        confidence: 0.92,
        operations: [
          { op: 'add', path: '/errorHandler', value: 'function() {}' },
          { op: 'replace', path: '/mainFunction', value: 'updated function' },
        ],
      };

      const message = generateCommitMessage(patch);
      
      expect(message).toContain('feat: Add error handling');
      expect(message).toContain('confidence: 92.0%');
      expect(message).toContain('1. ADD /errorHandler');
      expect(message).toContain('2. REPLACE /mainFunction');
    });
  });

  describe('Error Handling', () => {
    it('should handle GitHub API rate limits', async () => {
      const mockRateLimitResponse = {
        ok: false,
        status: 403,
        json: jest.fn().mockResolvedValue({
          message: 'API rate limit exceeded',
          documentation_url: 'https://docs.github.com/rest/overview/resources-in-the-rest-api#rate-limiting',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockRateLimitResponse as any);

      const makeGitHubRequest = async (url: string) => {
        const response = await fetch(url);
        
        if (!response.ok) {
          if (response.status === 403) {
            const data = await response.json();
            if (data.message.includes('rate limit')) {
              throw new Error('GitHub API rate limit exceeded. Please try again later.');
            }
          }
          throw new Error(`GitHub API error: ${response.status}`);
        }

        return response.json();
      };

      await expect(makeGitHubRequest('https://api.github.com/user')).rejects.toThrow('rate limit exceeded');
    });

    it('should handle invalid tokens', async () => {
      const mockUnauthorizedResponse = {
        ok: false,
        status: 401,
        json: jest.fn().mockResolvedValue({
          message: 'Bad credentials',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockUnauthorizedResponse as any);

      const makeAuthenticatedRequest = async (token: string) => {
        const response = await fetch('https://api.github.com/user', {
          headers: { 'Authorization': `Bearer ${token}` },
        });

        if (response.status === 401) {
          throw new Error('Invalid GitHub token. Please reconnect your account.');
        }

        return response.json();
      };

      await expect(makeAuthenticatedRequest('invalid-token')).rejects.toThrow('Invalid GitHub token');
    });
  });
});
