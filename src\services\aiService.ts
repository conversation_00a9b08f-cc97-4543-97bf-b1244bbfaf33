
interface AgentResponse {
  agent: 'planner' | 'critic';
  message: string;
  score?: number;
  patch?: string;
  approved?: boolean;
}

interface TransformationResult {
  originalCode: string;
  transformedCode: string;
  diff: string;
  iterations: number;
  finalScore: number;
}

export class AIService {
  private readonly SCORE_THRESHOLD = 0.9;
  private readonly MAX_ITERATIONS = 5;

  async transformCode(code: string, onLog: (entry: any) => void): Promise<TransformationResult> {
    let currentCode = code;
    let iterations = 0;
    let finalScore = 0;

    onLog({
      id: Date.now(),
      agent: 'system',
      message: 'Starting dual-AI transformation process...',
      timestamp: new Date()
    });

    while (iterations < this.MAX_ITERATIONS) {
      iterations++;
      
      // Planner generates improvement
      onLog({
        id: Date.now() + iterations,
        agent: 'planner',
        message: `Iteration ${iterations}: Analyzing code structure and generating optimizations...`,
        timestamp: new Date()
      });

      await this.delay(1000);

      const plannerResponse = await this.callPlannerAgent(currentCode);
      
      onLog({
        id: Date.now() + iterations + 1000,
        agent: 'planner',
        message: plannerResponse.message,
        timestamp: new Date()
      });

      await this.delay(1000);

      // Critic evaluates the improvement
      onLog({
        id: Date.now() + iterations + 2000,
        agent: 'critic',
        message: 'Reviewing proposed changes...',
        timestamp: new Date()
      });

      await this.delay(1000);

      const criticResponse = await this.callCriticAgent(currentCode, plannerResponse.patch);
      
      onLog({
        id: Date.now() + iterations + 3000,
        agent: 'critic',
        message: `${criticResponse.message} (Score: ${criticResponse.score?.toFixed(2)})`,
        timestamp: new Date()
      });

      finalScore = criticResponse.score || 0;

      if (criticResponse.approved && criticResponse.score && criticResponse.score >= this.SCORE_THRESHOLD) {
        onLog({
          id: Date.now() + iterations + 4000,
          agent: 'system',
          message: `Transformation completed! Final score: ${criticResponse.score.toFixed(2)}`,
          timestamp: new Date()
        });
        
        currentCode = this.applyPatch(currentCode, plannerResponse.patch);
        break;
      }

      await this.delay(500);
    }

    const diff = this.generateDiff(code, currentCode);
    
    return {
      originalCode: code,
      transformedCode: currentCode,
      diff,
      iterations,
      finalScore
    };
  }

  private async callPlannerAgent(code: string): Promise<AgentResponse> {
    // Simulate API call to planner agent
    await this.delay(500);
    
    const improvements = this.analyzeCodeForImprovements(code);
    const patch = this.generatePatch(code, improvements);
    
    return {
      agent: 'planner',
      message: `Generated ${improvements.length} optimization(s): ${improvements.join(', ')}`,
      patch
    };
  }

  private async callCriticAgent(originalCode: string, patch?: string): Promise<AgentResponse> {
    // Simulate API call to critic agent
    await this.delay(500);
    
    if (!patch) {
      return {
        agent: 'critic',
        message: 'No patch provided for evaluation',
        score: 0,
        approved: false
      };
    }

    const patchedCode = this.applyPatch(originalCode, patch);
    const score = this.evaluateCode(patchedCode);
    const approved = score >= this.SCORE_THRESHOLD;
    
    const feedback = approved 
      ? 'Patch approved! Code quality significantly improved.'
      : 'Patch needs refinement. Suggesting further improvements.';
    
    return {
      agent: 'critic',
      message: feedback,
      score,
      approved
    };
  }

  private analyzeCodeForImprovements(code: string): string[] {
    const improvements: string[] = [];
    
    // Check for recursive functions without memoization
    if (code.includes('return fibonacci(') && !code.includes('memo')) {
      improvements.push('Add memoization to recursive function');
    }
    
    // Check for inefficient loops
    if (code.includes('for') && code.includes('push')) {
      improvements.push('Optimize array operations');
    }
    
    // Check for missing error handling
    if (code.includes('JSON.parse') && !code.includes('try')) {
      improvements.push('Add error handling');
    }
    
    // Default improvement if none found
    if (improvements.length === 0) {
      improvements.push('Code structure optimization');
    }
    
    return improvements;
  }

  private generatePatch(originalCode: string, improvements: string[]): string {
    // For the fibonacci example, add memoization
    if (improvements.includes('Add memoization to recursive function')) {
      return originalCode.replace(
        /function fibonacci\(n\) \{[\s\S]*?\}/,
        `function fibonacci(n, memo = {}) {
  if (n in memo) return memo[n];
  if (n <= 1) return n;
  
  memo[n] = fibonacci(n - 1, memo) + fibonacci(n - 2, memo);
  return memo[n];
}`
      );
    }
    
    // Add some basic optimizations
    return originalCode
      .replace(/var /g, 'const ')
      .replace(/function /g, 'const ')
      .replace(/\{/g, ' => {');
  }

  private applyPatch(originalCode: string, patch: string): string {
    return patch;
  }

  private evaluateCode(code: string): number {
    let score = 0.5; // Base score
    
    // Positive factors
    if (code.includes('memo')) score += 0.3; // Memoization
    if (code.includes('const')) score += 0.1; // Modern syntax
    if (code.includes('=>')) score += 0.1; // Arrow functions
    if (code.includes('try')) score += 0.1; // Error handling
    
    // Ensure score is between 0 and 1
    return Math.min(Math.max(score, 0), 1);
  }

  private generateDiff(original: string, modified: string): string {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    let diff = `--- Original\n+++ Optimized\n@@ -1,${originalLines.length} +1,${modifiedLines.length} @@\n`;
    
    // Simple diff generation
    originalLines.forEach(line => {
      diff += `-${line}\n`;
    });
    
    modifiedLines.forEach(line => {
      diff += `+${line}\n`;
    });
    
    return diff;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
