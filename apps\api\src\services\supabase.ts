import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'your-anon-key';

export const supabaseClient = createClient(supabaseUrl, supabaseKey);

export interface AgentLogEntry {
  id?: string;
  session_id: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch?: any;
  created_at?: string;
}

export interface ReactorSession {
  id?: string;
  prompt: string;
  max_loops: number;
  final_score?: number;
  final_patch?: any;
  iterations_count?: number;
  status?: 'running' | 'completed' | 'failed';
  github_pr_url?: string;
  created_at?: string;
  completed_at?: string;
}

export class SupabaseService {
  async createSession(session: Omit<ReactorSession, 'id' | 'created_at'>): Promise<ReactorSession> {
    const { data, error } = await supabaseClient
      .from('reactor_sessions')
      .insert(session)
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to create session: ${error.message}`);
    }
    
    return data;
  }

  async updateSession(id: string, updates: Partial<ReactorSession>): Promise<ReactorSession> {
    const { data, error } = await supabaseClient
      .from('reactor_sessions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to update session: ${error.message}`);
    }
    
    return data;
  }

  async logIteration(log: Omit<AgentLogEntry, 'id' | 'created_at'>): Promise<AgentLogEntry> {
    const { data, error } = await supabaseClient
      .from('agent_logs')
      .insert(log)
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to log iteration: ${error.message}`);
    }
    
    return data;
  }

  async getSessionLogs(sessionId: string): Promise<AgentLogEntry[]> {
    const { data, error } = await supabaseClient
      .from('agent_logs')
      .select('*')
      .eq('session_id', sessionId)
      .order('iteration', { ascending: true });
    
    if (error) {
      throw new Error(`Failed to get session logs: ${error.message}`);
    }
    
    return data || [];
  }

  async getSession(id: string): Promise<ReactorSession | null> {
    const { data, error } = await supabaseClient
      .from('reactor_sessions')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get session: ${error.message}`);
    }
    
    return data;
  }
}

export const supabaseService = new SupabaseService();
