import { renderHook, act } from '@testing-library/react';
import { useSupabaseReactor } from '../useSupabaseReactor';
import { supabase } from '@/lib/supabase';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
    },
    from: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('useSupabaseReactor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful session
    mockSupabase.auth.getSession.mockResolvedValue({
      data: {
        session: {
          access_token: 'mock-token',
          user: { id: 'user-123' },
        },
      },
      error: null,
    });

    // Mock Supabase from() chain
    const mockInsert = jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: { id: 'transformation-123' },
          error: null,
        }),
      }),
    });

    const mockUpdate = jest.fn().mockReturnValue({
      eq: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
    });

    mockSupabase.from.mockReturnValue({
      insert: mockInsert,
      update: mockUpdate,
    } as any);
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useSupabaseReactor());

    expect(result.current.isRunning).toBe(false);
    expect(result.current.events).toEqual([]);
    expect(result.current.currentPatch).toBeNull();
    expect(result.current.finalResult).toBeNull();
    expect(result.current.progress).toBe(0);
    expect(result.current.transformationId).toBeNull();
  });

  it('should run loop successfully', async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        success: true,
        finalPatch: { operations: [] },
        finalScore: 0.95,
        iterations: 3,
        logs: [
          {
            iteration: 1,
            plan: 'Test plan',
            critique: 'Test critique',
            score: 0.8,
            patch: { operations: [] },
          },
        ],
        completed: true,
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSupabaseReactor());

    await act(async () => {
      await result.current.runLoop('Test prompt', 5);
    });

    expect(result.current.isRunning).toBe(false);
    expect(result.current.events.length).toBeGreaterThan(0);
    expect(result.current.finalResult).toBeTruthy();
    expect(result.current.transformationId).toBe('transformation-123');
  });

  it('should handle authentication error', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    const { result } = renderHook(() => useSupabaseReactor());

    await act(async () => {
      await result.current.runLoop('Test prompt');
    });

    expect(result.current.isRunning).toBe(false);
    expect(result.current.events.some(e => e.type === 'error')).toBe(true);
  });

  it('should handle API error', async () => {
    const mockResponse = {
      ok: false,
      json: jest.fn().mockResolvedValue({
        error: 'API Error',
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSupabaseReactor());

    await act(async () => {
      await result.current.runLoop('Test prompt');
    });

    expect(result.current.isRunning).toBe(false);
    expect(result.current.events.some(e => e.type === 'error')).toBe(true);
  });

  it('should stop loop when requested', async () => {
    const { result } = renderHook(() => useSupabaseReactor());

    act(() => {
      result.current.stopLoop();
    });

    expect(result.current.isRunning).toBe(false);
    expect(result.current.progress).toBe(0);
    expect(result.current.events.some(e => e.content.includes('stopped'))).toBe(true);
  });

  it('should clear events', () => {
    const { result } = renderHook(() => useSupabaseReactor());

    act(() => {
      result.current.clearEvents();
    });

    expect(result.current.events).toEqual([]);
    expect(result.current.currentPatch).toBeNull();
    expect(result.current.finalResult).toBeNull();
    expect(result.current.progress).toBe(0);
    expect(result.current.transformationId).toBeNull();
  });

  it('should create PR successfully', async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        success: true,
        pr: {
          url: 'https://github.com/user/repo/pull/123',
          number: 123,
        },
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSupabaseReactor());

    // Set transformation ID
    act(() => {
      (result.current as any).setTransformationId('transformation-123');
    });

    await act(async () => {
      await result.current.createPR();
    });

    expect(result.current.events.some(e => e.content.includes('Pull Request created'))).toBe(true);
  });

  it('should handle PR creation error', async () => {
    const { result } = renderHook(() => useSupabaseReactor());

    await act(async () => {
      try {
        await result.current.createPR();
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    expect(result.current.events.some(e => e.type === 'error')).toBe(true);
  });
});
