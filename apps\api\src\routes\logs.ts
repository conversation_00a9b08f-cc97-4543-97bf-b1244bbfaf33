import { Router } from 'express';
import { z } from 'zod';
import { loggingService, LogEntry } from '../services/loggingService.js';
import { strictRateLimit } from '../middleware/security.js';

const router = Router();

// Request validation schemas
const logEntrySchema = z.object({
  level: z.enum(['error', 'warn', 'info', 'debug']),
  message: z.string().min(1, 'Message is required'),
  metadata: z.record(z.any()).optional(),
  service: z.string().min(1, 'Service is required'),
  user_id: z.string().uuid().optional(),
  session_id: z.string().uuid().optional(),
  request_id: z.string().optional()
});

const getLogsSchema = z.object({
  service: z.string().optional(),
  level: z.enum(['error', 'warn', 'info', 'debug']).optional(),
  limit: z.coerce.number().min(1).max(1000).default(100)
});

const getMetricsSchema = z.object({
  timeRange: z.enum(['1h', '6h', '24h', '7d', '30d']).default('24h')
});

// Middleware to extract request context
const addRequestContext = (req: any, res: any, next: any) => {
  req.requestContext = {
    request_id: req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    user_id: req.user?.id,
    ip: req.ip,
    user_agent: req.get('User-Agent')
  };
  next();
};

// Apply rate limiting to logs endpoints
router.use(strictRateLimit);
router.use(addRequestContext);

// POST /api/logs - Store a log entry
router.post('/logs', async (req, res) => {
  try {
    const validatedRequest = logEntrySchema.parse(req.body);
    
    const logEntry: LogEntry = {
      ...validatedRequest,
      request_id: req.requestContext.request_id,
      user_id: validatedRequest.user_id || req.requestContext.user_id,
      timestamp: new Date().toISOString()
    };
    
    await loggingService.log(logEntry);
    
    res.json({
      success: true,
      message: 'Log entry stored successfully',
      request_id: req.requestContext.request_id
    });
  } catch (error) {
    console.error('Store log entry error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to store log entry',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/logs - Retrieve recent logs
router.get('/logs', async (req, res) => {
  try {
    const { service, level, limit } = getLogsSchema.parse(req.query);
    
    const logs = await loggingService.getRecentLogs(service, level, limit);
    
    res.json({
      success: true,
      data: logs,
      count: logs.length,
      filters: { service, level, limit }
    });
  } catch (error) {
    console.error('Get logs error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve logs',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/logs/metrics - Get metrics for monitoring dashboards
router.get('/logs/metrics', async (req, res) => {
  try {
    const { timeRange } = getMetricsSchema.parse(req.query);
    
    const metrics = await loggingService.getMetrics(timeRange);
    
    res.json({
      success: true,
      data: metrics,
      timeRange,
      generated_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Get metrics error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/logs/health - Health check endpoint for monitoring
router.get('/logs/health', async (req, res) => {
  try {
    // Check if we can write and read logs
    const testLogEntry: LogEntry = {
      level: 'info',
      message: 'Health check test log',
      service: 'logging-service',
      metadata: {
        health_check: true,
        timestamp: new Date().toISOString()
      }
    };
    
    await loggingService.log(testLogEntry);
    
    // Try to retrieve recent logs
    const recentLogs = await loggingService.getRecentLogs('logging-service', 'info', 1);
    
    const isHealthy = recentLogs.length > 0;
    
    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        log_write: true,
        log_read: recentLogs.length > 0,
        database_connection: true
      }
    });
  } catch (error) {
    console.error('Logging health check error:', error);
    
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      checks: {
        log_write: false,
        log_read: false,
        database_connection: false
      }
    });
  }
});

// POST /api/logs/bulk - Store multiple log entries at once
router.post('/logs/bulk', async (req, res) => {
  try {
    const bulkLogsSchema = z.object({
      logs: z.array(logEntrySchema).min(1).max(100) // Limit to 100 logs per request
    });
    
    const { logs } = bulkLogsSchema.parse(req.body);
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (const logData of logs) {
      try {
        const logEntry: LogEntry = {
          ...logData,
          request_id: req.requestContext.request_id,
          user_id: logData.user_id || req.requestContext.user_id,
          timestamp: new Date().toISOString()
        };
        
        await loggingService.log(logEntry);
        results.push({ success: true, message: logData.message });
        successCount++;
      } catch (error) {
        results.push({ 
          success: false, 
          message: logData.message, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        errorCount++;
      }
    }
    
    res.json({
      success: errorCount === 0,
      message: `Processed ${logs.length} log entries`,
      summary: {
        total: logs.length,
        successful: successCount,
        failed: errorCount
      },
      results: results,
      request_id: req.requestContext.request_id
    });
  } catch (error) {
    console.error('Bulk log storage error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to store bulk log entries',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/logs/search - Search logs with filters
router.get('/logs/search', async (req, res) => {
  try {
    const searchSchema = z.object({
      query: z.string().min(1, 'Search query is required'),
      service: z.string().optional(),
      level: z.enum(['error', 'warn', 'info', 'debug']).optional(),
      start_date: z.string().datetime().optional(),
      end_date: z.string().datetime().optional(),
      limit: z.coerce.number().min(1).max(500).default(50)
    });
    
    const { query, service, level, start_date, end_date, limit } = searchSchema.parse(req.query);
    
    // This is a simplified search - in production, you might want to use full-text search
    let dbQuery = loggingService.supabaseClient
      .from('logs')
      .select('*')
      .ilike('message', `%${query}%`)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (service) {
      dbQuery = dbQuery.eq('service', service);
    }
    
    if (level) {
      dbQuery = dbQuery.eq('level', level);
    }
    
    if (start_date) {
      dbQuery = dbQuery.gte('created_at', start_date);
    }
    
    if (end_date) {
      dbQuery = dbQuery.lte('created_at', end_date);
    }
    
    const { data, error } = await dbQuery;
    
    if (error) {
      throw new Error(`Search failed: ${error.message}`);
    }
    
    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0,
      query: {
        search: query,
        service,
        level,
        start_date,
        end_date,
        limit
      }
    });
  } catch (error) {
    console.error('Log search error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to search logs',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as logsRouter };
