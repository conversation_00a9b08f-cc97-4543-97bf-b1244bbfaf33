import { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Play, Square, RotateCcw } from 'lucide-react';

interface StreamEvent {
  id: string;
  timestamp: Date;
  type: 'plan' | 'critique' | 'iteration' | 'complete' | 'error';
  content: string;
  score?: number;
  iteration?: number;
}

interface StreamPanelProps {
  isStreaming: boolean;
  onStart: () => void;
  onStop: () => void;
  onClear: () => void;
  events: StreamEvent[];
}

export const StreamPanel = ({ 
  isStreaming, 
  onStart, 
  onStop, 
  onClear, 
  events 
}: StreamPanelProps) => {
  const [progress, setProgress] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new events arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [events]);

  // Update progress based on events
  useEffect(() => {
    const iterations = events.filter(e => e.type === 'iteration').length;
    const maxIterations = 10; // Default max loops
    setProgress((iterations / maxIterations) * 100);
  }, [events]);

  const getEventIcon = (type: StreamEvent['type']) => {
    switch (type) {
      case 'plan':
        return '🧠';
      case 'critique':
        return '🔍';
      case 'iteration':
        return '🔄';
      case 'complete':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '📝';
    }
  };

  const getEventColor = (type: StreamEvent['type']) => {
    switch (type) {
      case 'plan':
        return 'text-indigo-400';
      case 'critique':
        return 'text-purple-400';
      case 'iteration':
        return 'text-blue-400';
      case 'complete':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-slate-400';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  return (
    <div className="h-full bg-slate-900 flex flex-col">
      {/* Header with Controls */}
      <div className="p-4 border-b border-slate-800">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-white">Real-time Stream</h3>
          <div className="flex items-center space-x-2">
            {!isStreaming ? (
              <Button
                size="sm"
                onClick={onStart}
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
              >
                <Play className="w-3 h-3 mr-1" />
                Start
              </Button>
            ) : (
              <Button
                size="sm"
                variant="destructive"
                onClick={onStop}
              >
                <Square className="w-3 h-3 mr-1" />
                Stop
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={onClear}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Clear
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        {isStreaming && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-slate-400">Progress</span>
              <span className="text-slate-400">{Math.round(progress)}%</span>
            </div>
            <Progress 
              value={progress} 
              className="h-2 bg-slate-800"
            />
          </div>
        )}

        {/* Status */}
        <div className="flex items-center space-x-2 mt-3">
          {isStreaming ? (
            <>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-400">Streaming active</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
              <span className="text-xs text-slate-400">Ready to stream</span>
            </>
          )}
          <Badge variant="outline" className="text-xs">
            {events.length} events
          </Badge>
        </div>
      </div>

      {/* Event Stream */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div ref={scrollRef} className="p-4 space-y-3">
            {events.length === 0 ? (
              <div className="text-center text-slate-500 py-8">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-slate-800 flex items-center justify-center">
                  <Play className="w-6 h-6 text-slate-600" />
                </div>
                <p className="text-sm">No stream events yet</p>
                <p className="text-xs text-slate-600">Start the reactor loop to see real-time updates</p>
              </div>
            ) : (
              events.map((event) => (
                <div
                  key={event.id}
                  className="bg-slate-800 rounded-lg p-3 border border-slate-700"
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-lg">{getEventIcon(event.type)}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`text-xs font-medium ${getEventColor(event.type)}`}>
                          {event.type.toUpperCase()}
                        </span>
                        {event.iteration && (
                          <Badge variant="outline" className="text-xs">
                            Iteration {event.iteration}
                          </Badge>
                        )}
                        {event.score !== undefined && (
                          <Badge 
                            className={`text-xs ${
                              event.score >= 0.95 
                                ? 'bg-green-600/20 text-green-300 border-green-500/30'
                                : event.score >= 0.8
                                ? 'bg-yellow-600/20 text-yellow-300 border-yellow-500/30'
                                : 'bg-red-600/20 text-red-300 border-red-500/30'
                            }`}
                          >
                            Score: {(event.score * 100).toFixed(1)}%
                          </Badge>
                        )}
                        <span className="text-xs text-slate-500">
                          {formatTimestamp(event.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm text-slate-300 break-words">
                        {event.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
