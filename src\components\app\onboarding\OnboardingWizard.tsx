import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  ArrowLeft, 
  Rocket,
  Key,
  Github,
  Zap,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { APIKeySetup } from './APIKeySetup';
import { GitHubConnection } from './GitHubConnection';
import { SampleLoop } from './SampleLoop';

interface OnboardingStep {
  id: 'api_keys' | 'github_connection' | 'sample_loop';
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  optional?: boolean;
}

interface OnboardingProgress {
  completed: boolean;
  current_step: number;
  steps: {
    api_keys: boolean;
    github_connection: boolean;
    sample_loop: boolean;
  };
}

interface OnboardingWizardProps {
  onComplete?: () => void;
  onSkip?: () => void;
  autoStart?: boolean;
}

export const OnboardingWizard = ({ 
  onComplete, 
  onSkip, 
  autoStart = true 
}: OnboardingWizardProps) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [progress, setProgress] = useState<OnboardingProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const { toast } = useToast();

  const steps: OnboardingStep[] = [
    {
      id: 'api_keys',
      title: 'API Keys Setup',
      description: 'Configure your AI provider API keys (OpenAI, Anthropic)',
      icon: <Key className="h-5 w-5" />,
      completed: progress?.steps.api_keys || false,
    },
    {
      id: 'github_connection',
      title: 'GitHub Integration',
      description: 'Connect your GitHub account for PR creation',
      icon: <Github className="h-5 w-5" />,
      completed: progress?.steps.github_connection || false,
      optional: true,
    },
    {
      id: 'sample_loop',
      title: 'Test Run',
      description: 'Run your first reactor loop to see how it works',
      icon: <Zap className="h-5 w-5" />,
      completed: progress?.steps.sample_loop || false,
    },
  ];

  const fetchOnboardingStatus = async () => {
    try {
      const response = await fetch('/api/onboarding/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('supabase_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch onboarding status');
      }

      const result = await response.json();
      
      if (result.success) {
        setProgress(result.progress);
        setCurrentStepIndex(result.currentStep - 1);
        
        if (!result.needsOnboarding) {
          setIsVisible(false);
          return;
        }
        
        if (autoStart) {
          setIsVisible(true);
        }
      }
    } catch (err) {
      console.error('Failed to fetch onboarding status:', err);
      // Show onboarding anyway if we can't determine status
      if (autoStart) {
        setIsVisible(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const updateStepProgress = async (stepId: OnboardingStep['id'], completed: boolean = true) => {
    try {
      const response = await fetch('/api/onboarding/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase_token')}`,
        },
        body: JSON.stringify({
          step: stepId,
          completed,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update progress');
      }

      const result = await response.json();
      
      if (result.success) {
        setProgress(result.progress);
        
        toast({
          title: 'Progress Saved',
          description: `${steps.find(s => s.id === stepId)?.title} completed!`,
        });
      }
    } catch (err) {
      console.error('Failed to update progress:', err);
      toast({
        title: 'Progress Error',
        description: 'Failed to save progress, but you can continue.',
        variant: 'destructive',
      });
    }
  };

  const handleStepComplete = async (stepId: OnboardingStep['id']) => {
    await updateStepProgress(stepId, true);
    
    // Move to next step if not at the end
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      // All steps completed
      handleComplete();
    }
  };

  const handleComplete = async () => {
    try {
      // Mark all remaining steps as completed
      const incompleteSteps = steps.filter(step => !step.completed);
      for (const step of incompleteSteps) {
        await updateStepProgress(step.id, true);
      }
      
      toast({
        title: 'Onboarding Complete!',
        description: 'Welcome to Metamorphic Reactor. You\'re ready to start transforming code!',
      });
      
      setIsVisible(false);
      onComplete?.();
    } catch (err) {
      console.error('Failed to complete onboarding:', err);
    }
  };

  const handleSkip = async () => {
    try {
      const response = await fetch('/api/onboarding/skip', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('supabase_token')}`,
        },
      });

      if (response.ok) {
        toast({
          title: 'Onboarding Skipped',
          description: 'You can access setup options in Settings anytime.',
        });
      }
    } catch (err) {
      console.error('Failed to skip onboarding:', err);
    }
    
    setIsVisible(false);
    onSkip?.();
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  useEffect(() => {
    fetchOnboardingStatus();
  }, []);

  if (loading || !isVisible) {
    return null;
  }

  const currentStep = steps[currentStepIndex];
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;
  const completedSteps = steps.filter(step => step.completed).length;

  const renderStepContent = () => {
    switch (currentStep.id) {
      case 'api_keys':
        return (
          <APIKeySetup 
            onComplete={() => handleStepComplete('api_keys')}
            onSkip={() => handleNext()}
          />
        );
      case 'github_connection':
        return (
          <GitHubConnection 
            onComplete={() => handleStepComplete('github_connection')}
            onSkip={() => handleNext()}
          />
        );
      case 'sample_loop':
        return (
          <SampleLoop 
            onComplete={() => handleStepComplete('sample_loop')}
            onSkip={() => handleComplete()}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Rocket className="h-6 w-6 text-agent-planner" />
                Welcome to Metamorphic Reactor
              </CardTitle>
              <CardDescription>
                Let's get you set up in just a few steps
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Step {currentStepIndex + 1} of {steps.length}</span>
              <span>{completedSteps}/{steps.length} completed</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Step Indicators */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center gap-2 ${
                  index === currentStepIndex ? 'text-primary' : 
                  step.completed ? 'text-green-600' : 'text-muted-foreground'
                }`}
              >
                {step.completed ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <Circle className="h-5 w-5" />
                )}
                <div className="hidden sm:block">
                  <p className="text-sm font-medium">{step.title}</p>
                  {step.optional && (
                    <Badge variant="outline" className="text-xs bg-info/10 text-info-foreground">Optional</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Current Step Content */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              {currentStep.icon}
              <div>
                <h3 className="text-lg font-semibold">{currentStep.title}</h3>
                <p className="text-muted-foreground">{currentStep.description}</p>
              </div>
            </div>

            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStepIndex === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex gap-2">
              {currentStep.optional && (
                <Button variant="ghost" onClick={handleNext}>
                  Skip
                </Button>
              )}
              
              <Button variant="ghost" onClick={handleSkip}>
                Skip Setup
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
