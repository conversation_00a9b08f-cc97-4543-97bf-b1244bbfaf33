import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Switch } from './ui/switch';
import { Badge } from './ui/badge';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Separator } from './ui/separator';
import { <PERSON>ie, Settings, Shield, BarChart3, Palette } from 'lucide-react';
import { useToast } from './ui/use-toast';

interface CookiePreferences {
  essential: boolean;
  analytics: boolean;
  preferences: boolean;
  marketing: boolean;
}

interface CookieConsentProps {
  onPreferencesChange?: (preferences: CookiePreferences) => void;
}

export const CookieConsent: React.FC<CookieConsentProps> = ({
  onPreferencesChange
}) => {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true, // Always true, cannot be disabled
    analytics: false,
    preferences: false,
    marketing: false
  });
  const { toast } = useToast();

  // Check if user has already made a choice
  useEffect(() => {
    const savedPreferences = localStorage.getItem('cookie-preferences');
    const consentGiven = localStorage.getItem('cookie-consent-given');
    
    if (consentGiven) {
      // User has already made a choice
      if (savedPreferences) {
        const parsed = JSON.parse(savedPreferences);
        setPreferences(parsed);
        onPreferencesChange?.(parsed);
      }
    } else {
      // Show banner for first-time visitors
      setShowBanner(true);
    }
  }, [onPreferencesChange]);

  // Save preferences to localStorage and apply them
  const savePreferences = (newPreferences: CookiePreferences) => {
    localStorage.setItem('cookie-preferences', JSON.stringify(newPreferences));
    localStorage.setItem('cookie-consent-given', 'true');
    localStorage.setItem('cookie-consent-date', new Date().toISOString());
    
    setPreferences(newPreferences);
    onPreferencesChange?.(newPreferences);
    
    // Apply cookie preferences
    applyCookiePreferences(newPreferences);
  };

  // Apply cookie preferences by enabling/disabling tracking
  const applyCookiePreferences = (prefs: CookiePreferences) => {
    // Analytics cookies (Google Analytics, etc.)
    if (prefs.analytics) {
      // Enable analytics tracking
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          analytics_storage: 'granted'
        });
      }
    } else {
      // Disable analytics tracking
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          analytics_storage: 'denied'
        });
      }
    }

    // Marketing cookies (advertising, social media)
    if (prefs.marketing) {
      // Enable marketing tracking
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          ad_storage: 'granted',
          ad_user_data: 'granted',
          ad_personalization: 'granted'
        });
      }
    } else {
      // Disable marketing tracking
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          ad_storage: 'denied',
          ad_user_data: 'denied',
          ad_personalization: 'denied'
        });
      }
    }

    // Preferences cookies are handled by the application itself
    if (!prefs.preferences) {
      // Clear non-essential preference cookies
      const cookiesToClear = ['theme', 'language', 'ui-preferences'];
      cookiesToClear.forEach(name => {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });
    }
  };

  // Accept all cookies
  const acceptAll = () => {
    const allAccepted: CookiePreferences = {
      essential: true,
      analytics: true,
      preferences: true,
      marketing: true
    };
    
    savePreferences(allAccepted);
    setShowBanner(false);
    
    toast({
      title: "Cookie Preferences Saved",
      description: "All cookies have been accepted."
    });
  };

  // Accept only essential cookies
  const acceptEssential = () => {
    const essentialOnly: CookiePreferences = {
      essential: true,
      analytics: false,
      preferences: false,
      marketing: false
    };
    
    savePreferences(essentialOnly);
    setShowBanner(false);
    
    toast({
      title: "Cookie Preferences Saved",
      description: "Only essential cookies will be used."
    });
  };

  // Save custom preferences
  const saveCustomPreferences = () => {
    savePreferences(preferences);
    setShowBanner(false);
    setShowSettings(false);
    
    toast({
      title: "Cookie Preferences Saved",
      description: "Your custom preferences have been applied."
    });
  };

  // Update individual preference
  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Cookie categories information
  const cookieCategories = [
    {
      id: 'essential',
      name: 'Essential Cookies',
      icon: <Shield className="w-4 h-4" />,
      description: 'Required for basic website functionality, security, and authentication.',
      examples: ['Session tokens', 'CSRF protection', 'Load balancing'],
      required: true
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      icon: <BarChart3 className="w-4 h-4" />,
      description: 'Help us understand how visitors use our website to improve performance.',
      examples: ['Google Analytics', 'Usage statistics', 'Performance monitoring'],
      required: false
    },
    {
      id: 'preferences',
      name: 'Preference Cookies',
      icon: <Palette className="w-4 h-4" />,
      description: 'Remember your settings and preferences for a personalized experience.',
      examples: ['Theme selection', 'Language preference', 'UI customizations'],
      required: false
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      icon: <Cookie className="w-4 h-4" />,
      description: 'Used to deliver relevant advertisements and measure campaign effectiveness.',
      examples: ['Ad targeting', 'Social media integration', 'Conversion tracking'],
      required: false
    }
  ];

  // Cookie consent banner
  const ConsentBanner = () => {
    if (!showBanner) return null;

    return (
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background border-t shadow-lg">
        <div className="container mx-auto max-w-6xl">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-4 lg:space-y-0 lg:space-x-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Cookie className="w-5 h-5" />
                    <h3 className="font-semibold">Cookie Consent</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    We use cookies to enhance your experience, analyze site usage, and assist in marketing efforts. 
                    You can customize your preferences or accept all cookies.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <Dialog open={showSettings} onOpenChange={setShowSettings}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        Customize
                      </Button>
                    </DialogTrigger>
                    <CookieSettingsDialog />
                  </Dialog>
                  
                  <Button variant="outline" size="sm" onClick={acceptEssential}>
                    Essential Only
                  </Button>
                  
                  <Button size="sm" onClick={acceptAll}>
                    Accept All
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  // Cookie settings dialog
  const CookieSettingsDialog = () => (
    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center space-x-2">
          <Cookie className="w-5 h-5" />
          <span>Cookie Preferences</span>
        </DialogTitle>
      </DialogHeader>

      <div className="space-y-6">
        <p className="text-sm text-muted-foreground">
          Manage your cookie preferences below. Essential cookies are required for basic 
          functionality and cannot be disabled.
        </p>

        {cookieCategories.map((category) => (
          <Card key={category.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {category.icon}
                  <CardTitle className="text-base">{category.name}</CardTitle>
                  {category.required && (
                    <Badge variant="secondary" className="text-xs">
                      Required
                    </Badge>
                  )}
                </div>
                <Switch
                  checked={preferences[category.id as keyof CookiePreferences]}
                  onCheckedChange={(checked) => 
                    updatePreference(category.id as keyof CookiePreferences, checked)
                  }
                  disabled={category.required}
                />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-3">
                {category.description}
              </CardDescription>
              <div>
                <h5 className="text-xs font-medium mb-1">Examples:</h5>
                <div className="flex flex-wrap gap-1">
                  {category.examples.map((example) => (
                    <Badge key={example} variant="outline" className="text-xs">
                      {example}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        <Separator />

        <div className="flex justify-between">
          <Button variant="outline" onClick={() => setShowSettings(false)}>
            Cancel
          </Button>
          <div className="space-x-2">
            <Button variant="outline" onClick={acceptEssential}>
              Essential Only
            </Button>
            <Button onClick={saveCustomPreferences}>
              Save Preferences
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  );

  return (
    <>
      <ConsentBanner />
    </>
  );
};

// Hook to check if specific cookie types are allowed
export const useCookieConsent = () => {
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true,
    analytics: false,
    preferences: false,
    marketing: false
  });

  useEffect(() => {
    const savedPreferences = localStorage.getItem('cookie-preferences');
    if (savedPreferences) {
      setPreferences(JSON.parse(savedPreferences));
    }
  }, []);

  return {
    preferences,
    isAllowed: (category: keyof CookiePreferences) => preferences[category],
    hasConsent: () => localStorage.getItem('cookie-consent-given') === 'true'
  };
};
