
import { useState, useCallback } from 'react';

export interface TransformationHistoryEntry {
  id: string;
  timestamp: Date;
  originalCode: string;
  transformedCode: string;
  iterations: number;
  finalScore: number;
  title: string;
}

interface UseTransformationHistoryReturn {
  history: TransformationHistoryEntry[];
  addToHistory: (entry: Omit<TransformationHistoryEntry, 'id' | 'timestamp'>) => void;
  clearHistory: () => void;
  getHistoryEntry: (id: string) => TransformationHistoryEntry | undefined;
}

export const useTransformationHistory = (): UseTransformationHistoryReturn => {
  const [history, setHistory] = useState<TransformationHistoryEntry[]>([]);

  const addToHistory = useCallback((entry: Omit<TransformationHistoryEntry, 'id' | 'timestamp'>) => {
    const newEntry: TransformationHistoryEntry = {
      ...entry,
      id: `transformation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    setHistory(prev => [newEntry, ...prev].slice(0, 50)); // Keep only last 50 entries
  }, []);

  const clearHistory = useCallback(() => {
    setHistory([]);
  }, []);

  const getHistoryEntry = useCallback((id: string) => {
    return history.find(entry => entry.id === id);
  }, [history]);

  return {
    history,
    addToHistory,
    clearHistory,
    getHistoryEntry
  };
};
