import pino from 'pino';
import { supabaseClient } from './supabase.js';

export interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  metadata?: Record<string, any>;
  service: string;
  user_id?: string;
  session_id?: string;
  request_id?: string;
  timestamp?: string;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  window_minutes: number;
  webhook_url?: string;
  is_active: boolean;
}

class LoggingService {
  private logger: pino.Logger;
  private alertRules: AlertRule[] = [];

  constructor() {
    // Configure Pino logger
    this.logger = pino({
      level: process.env.LOG_LEVEL || 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname'
        }
      } : undefined,
      formatters: {
        level: (label) => {
          return { level: label };
        }
      },
      timestamp: pino.stdTimeFunctions.isoTime
    });

    // Initialize alert rules
    this.initializeAlertRules();
  }

  /**
   * Log a message and optionally store in database
   */
  async log(entry: LogEntry): Promise<void> {
    const logData = {
      ...entry,
      timestamp: entry.timestamp || new Date().toISOString()
    };

    // Log to console/file via Pino
    this.logger[entry.level](logData, entry.message);

    // Store in database for persistent logging
    try {
      await this.storeLogEntry(logData);
    } catch (error) {
      this.logger.error({ error: error.message }, 'Failed to store log entry in database');
    }

    // Check alert rules
    if (entry.level === 'error' || entry.level === 'warn') {
      await this.checkAlertRules(entry);
    }
  }

  /**
   * Store log entry in Supabase
   */
  private async storeLogEntry(entry: LogEntry): Promise<void> {
    const { error } = await supabaseClient
      .from('logs')
      .insert({
        level: entry.level,
        message: entry.message,
        metadata: entry.metadata || {},
        service: entry.service,
        user_id: entry.user_id,
        session_id: entry.session_id,
        request_id: entry.request_id,
        created_at: entry.timestamp
      });

    if (error) {
      throw new Error(`Failed to store log entry: ${error.message}`);
    }
  }

  /**
   * Initialize default alert rules
   */
  private initializeAlertRules(): void {
    this.alertRules = [
      {
        id: 'error_rate_high',
        name: 'High Error Rate',
        condition: 'error_rate_percent > threshold',
        threshold: 2.0, // 2%
        window_minutes: 15,
        webhook_url: process.env.SLACK_WEBHOOK_URL,
        is_active: true
      },
      {
        id: 'cost_guard_triggered',
        name: 'Cost Guard Triggered',
        condition: 'cost_guard_triggered = true',
        threshold: 1,
        window_minutes: 5,
        webhook_url: process.env.SLACK_WEBHOOK_URL,
        is_active: true
      },
      {
        id: 'high_response_time',
        name: 'High Response Time',
        condition: 'avg_response_time > threshold',
        threshold: 5000, // 5 seconds
        window_minutes: 10,
        webhook_url: process.env.SLACK_WEBHOOK_URL,
        is_active: true
      },
      {
        id: 'failed_transformations',
        name: 'High Transformation Failure Rate',
        condition: 'failed_transformation_rate > threshold',
        threshold: 10.0, // 10%
        window_minutes: 30,
        webhook_url: process.env.SLACK_WEBHOOK_URL,
        is_active: true
      }
    ];
  }

  /**
   * Check alert rules against recent logs
   */
  private async checkAlertRules(entry: LogEntry): Promise<void> {
    for (const rule of this.alertRules) {
      if (!rule.is_active) continue;

      try {
        const shouldAlert = await this.evaluateAlertRule(rule, entry);
        if (shouldAlert) {
          await this.sendAlert(rule, entry);
        }
      } catch (error) {
        this.logger.error({ 
          error: error.message, 
          rule: rule.id 
        }, 'Failed to evaluate alert rule');
      }
    }
  }

  /**
   * Evaluate if an alert rule should trigger
   */
  private async evaluateAlertRule(rule: AlertRule, entry: LogEntry): Promise<boolean> {
    const windowStart = new Date(Date.now() - rule.window_minutes * 60 * 1000).toISOString();

    switch (rule.id) {
      case 'error_rate_high':
        return await this.checkErrorRate(windowStart, rule.threshold);
      
      case 'cost_guard_triggered':
        return entry.metadata?.cost_guard_triggered === true;
      
      case 'high_response_time':
        return await this.checkResponseTime(windowStart, rule.threshold);
      
      case 'failed_transformations':
        return await this.checkTransformationFailureRate(windowStart, rule.threshold);
      
      default:
        return false;
    }
  }

  /**
   * Check error rate percentage
   */
  private async checkErrorRate(windowStart: string, threshold: number): Promise<boolean> {
    const { data, error } = await supabaseClient
      .rpc('get_error_rate_metrics', {
        window_start: windowStart
      });

    if (error || !data || data.length === 0) {
      return false;
    }

    const latestMetric = data[0];
    return latestMetric.error_rate_percent > threshold;
  }

  /**
   * Check average response time
   */
  private async checkResponseTime(windowStart: string, threshold: number): Promise<boolean> {
    const { data, error } = await supabaseClient
      .from('logs')
      .select('metadata')
      .eq('service', 'api')
      .gte('created_at', windowStart)
      .not('metadata->response_time', 'is', null);

    if (error || !data || data.length === 0) {
      return false;
    }

    const responseTimes = data
      .map(log => log.metadata?.response_time)
      .filter(time => typeof time === 'number');

    if (responseTimes.length === 0) return false;

    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    return avgResponseTime > threshold;
  }

  /**
   * Check transformation failure rate
   */
  private async checkTransformationFailureRate(windowStart: string, threshold: number): Promise<boolean> {
    const { data, error } = await supabaseClient
      .from('transformations')
      .select('status')
      .gte('created_at', windowStart);

    if (error || !data || data.length === 0) {
      return false;
    }

    const totalTransformations = data.length;
    const failedTransformations = data.filter(t => t.status === 'failed').length;
    const failureRate = (failedTransformations / totalTransformations) * 100;

    return failureRate > threshold;
  }

  /**
   * Send alert notification
   */
  private async sendAlert(rule: AlertRule, entry: LogEntry): Promise<void> {
    const alertMessage = {
      text: `🚨 Alert: ${rule.name}`,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Alert Triggered*\n\n• *Rule:* ${rule.name}\n• *Service:* ${entry.service}\n• *Message:* ${entry.message}\n• *Time:* ${entry.timestamp}\n• *Threshold:* ${rule.threshold}`
          }
        }
      ]
    };

    if (rule.webhook_url) {
      try {
        const response = await fetch(rule.webhook_url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(alertMessage),
        });

        if (!response.ok) {
          throw new Error(`Webhook request failed: ${response.statusText}`);
        }

        this.logger.info({ rule: rule.id }, 'Alert sent successfully');
      } catch (error) {
        this.logger.error({ 
          error: error.message, 
          rule: rule.id 
        }, 'Failed to send alert');
      }
    }
  }

  /**
   * Get recent logs for debugging
   */
  async getRecentLogs(
    service?: string, 
    level?: string, 
    limit: number = 100
  ): Promise<LogEntry[]> {
    let query = supabaseClient
      .from('logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (service) {
      query = query.eq('service', service);
    }

    if (level) {
      query = query.eq('level', level);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch logs: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get metrics for Grafana
   */
  async getMetrics(timeRange: string = '24h'): Promise<any> {
    const windowStart = this.getTimeRangeStart(timeRange);

    const [errorRateData, costData] = await Promise.all([
      supabaseClient.rpc('get_error_rate_metrics', { window_start: windowStart }),
      supabaseClient.rpc('get_cost_metrics', { window_start: windowStart })
    ]);

    return {
      error_rate: errorRateData.data || [],
      cost_metrics: costData.data || [],
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Convert time range string to ISO timestamp
   */
  private getTimeRangeStart(timeRange: string): string {
    const now = new Date();
    const ranges: Record<string, number> = {
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };

    const milliseconds = ranges[timeRange] || ranges['24h'];
    return new Date(now.getTime() - milliseconds).toISOString();
  }
}

export const loggingService = new LoggingService();
