
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>ight, Bot, Eye, Zap, Github, Code, Cpu } from "lucide-react";

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-800/50 bg-slate-900/80 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-agent-planner to-agent-critic rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-foreground">Metamorphic Reactor</h1>
            </div>
            <Button
              onClick={() => navigate('/dashboard')}
              className="bg-gradient-to-r from-agent-planner to-agent-critic hover:opacity-90 text-white"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20 text-center" aria-label="Hero section">
        <div className="max-w-4xl mx-auto">
          <Badge className="mb-6 bg-purple-600/20 text-purple-300 border-purple-500/30 text-sm px-4 py-2">
            Dual-AI Code Assistant
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Transform Your Code with
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}AI Iteration
            </span>
          </h1>
          <p className="text-xl text-slate-300 mb-8 leading-relaxed">
            Watch as two AI agents collaborate to automatically improve your code through intelligent iteration. 
            The Planner generates optimizations while the Critic ensures quality - all in real-time.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => navigate('/dashboard')}
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg"
            >
              Launch Dashboard
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-border text-foreground hover:bg-accent px-8 py-3 text-lg"
            >
              <Github className="w-5 h-5 mr-2" />
              View on GitHub
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20" aria-label="Features and how it works">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            How It Works
          </h2>
          <p className="text-xl text-slate-400 max-w-2xl mx-auto">
            Our dual-agent system combines planning and criticism for superior code optimization
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mb-4">
                <Bot className="w-6 h-6 text-purple-400" />
              </div>
              <CardTitle className="text-white">Planner Agent</CardTitle>
              <CardDescription className="text-slate-400">
                Analyzes your code and generates intelligent optimization patches
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-slate-300 text-sm">
                <li>• Code structure analysis</li>
                <li>• Performance optimization</li>
                <li>• Best practice suggestions</li>
                <li>• Refactoring proposals</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                <Eye className="w-6 h-6 text-blue-400" />
              </div>
              <CardTitle className="text-white">Critic Agent</CardTitle>
              <CardDescription className="text-slate-400">
                Reviews and scores proposed changes for quality assurance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-slate-300 text-sm">
                <li>• Quality assessment</li>
                <li>• Security review</li>
                <li>• Performance impact</li>
                <li>• Improvement feedback</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center mb-4">
                <Code className="w-6 h-6 text-green-400" />
              </div>
              <CardTitle className="text-white">Auto Iteration</CardTitle>
              <CardDescription className="text-slate-400">
                Continuous improvement loop until quality threshold is met
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-slate-300 text-sm">
                <li>• Real-time feedback</li>
                <li>• Iterative refinement</li>
                <li>• Quality scoring</li>
                <li>• Automatic convergence</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20 text-center" aria-label="Call to action">
        <div className="bg-gradient-to-r from-agent-planner/20 to-agent-critic/20 rounded-2xl border border-agent-planner/30 p-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Ready to Transform Your Code?
          </h2>
          <p className="text-muted-foreground mb-8 text-lg">
            Experience the power of dual-AI code optimization
          </p>
          <Button
            onClick={() => navigate('/dashboard')}
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg"
          >
            Start Optimizing Now
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-slate-800 bg-slate-900/50">
        <div className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-md flex items-center justify-center">
                <Zap className="w-4 h-4 text-white" />
              </div>
              <span className="text-slate-400">Metamorphic Reactor</span>
            </div>
            <p className="text-slate-500 text-sm">
              Powered by dual-AI intelligence
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
