import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DiffViewer } from '../DiffViewer';

// Mock Monaco Editor
jest.mock('@monaco-editor/react', () => ({
  DiffEditor: ({ original, modified, onMount }: any) => {
    // Simulate Monaco editor mount
    if (onMount) {
      const mockEditor = {
        updateOptions: jest.fn(),
      };
      onMount(mockEditor);
    }
    
    return (
      <div data-testid="monaco-diff-editor">
        <div data-testid="original-code">{original}</div>
        <div data-testid="modified-code">{modified}</div>
      </div>
    );
  },
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn().mockResolvedValue(undefined),
  },
});

describe('DiffViewer', () => {
  const mockPatch = {
    operations: [
      { op: 'add', path: '/test', value: 'test-value' },
      { op: 'replace', path: '/existing', value: 'new-value' },
    ],
    description: 'Test patch description',
    confidence: 0.85,
  };

  const mockDiffContent = `--- a/test.js
+++ b/test.js
@@ -1,3 +1,4 @@
 function test() {
+  console.log('added line');
   return true;
 }`;

  it('should render empty state when no content provided', () => {
    render(<DiffViewer />);
    
    expect(screen.getByText('No changes available')).toBeInTheDocument();
    expect(screen.getByText('Run the reactor loop to see code transformations')).toBeInTheDocument();
  });

  it('should render diff view with traditional diff content', () => {
    render(<DiffViewer diffContent={mockDiffContent} />);
    
    expect(screen.getByText('Diff')).toBeInTheDocument();
    expect(screen.getByText('function test() {')).toBeInTheDocument();
    expect(screen.getByText("console.log('added line');")).toBeInTheDocument();
  });

  it('should render Monaco diff editor with original and modified code', () => {
    const originalCode = 'function old() { return false; }';
    const modifiedCode = 'function new() { return true; }';
    
    render(
      <DiffViewer 
        originalCode={originalCode}
        modifiedCode={modifiedCode}
        language="javascript"
      />
    );
    
    expect(screen.getByTestId('monaco-diff-editor')).toBeInTheDocument();
    expect(screen.getByTestId('original-code')).toHaveTextContent(originalCode);
    expect(screen.getByTestId('modified-code')).toHaveTextContent(modifiedCode);
  });

  it('should render patch view with operations', () => {
    render(<DiffViewer patch={mockPatch} />);
    
    // Switch to patch view
    fireEvent.click(screen.getByText('Patch'));
    
    expect(screen.getByText('Patch Description')).toBeInTheDocument();
    expect(screen.getByText('Test patch description')).toBeInTheDocument();
    expect(screen.getByText('Confidence: 85.0%')).toBeInTheDocument();
    expect(screen.getByText('Operations')).toBeInTheDocument();
    expect(screen.getByText('ADD')).toBeInTheDocument();
    expect(screen.getByText('REPLACE')).toBeInTheDocument();
  });

  it('should switch between view modes', () => {
    render(<DiffViewer diffContent={mockDiffContent} patch={mockPatch} />);
    
    // Default should be diff view
    expect(screen.getByText('function test() {')).toBeInTheDocument();
    
    // Switch to patch view
    fireEvent.click(screen.getByText('Patch'));
    expect(screen.getByText('Patch Description')).toBeInTheDocument();
    
    // Switch to preview view
    fireEvent.click(screen.getByText('Preview'));
    expect(screen.getByText('Preview mode coming soon')).toBeInTheDocument();
  });

  it('should handle copy to clipboard', async () => {
    render(<DiffViewer diffContent={mockDiffContent} />);
    
    const copyButton = screen.getByText('Copy');
    fireEvent.click(copyButton);
    
    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(mockDiffContent);
    });
    
    expect(screen.getByText('Copied!')).toBeInTheDocument();
    
    // Should revert back to "Copy" after timeout
    await waitFor(() => {
      expect(screen.getByText('Copy')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('should call onDownload when download button is clicked', () => {
    const mockOnDownload = jest.fn();
    render(<DiffViewer diffContent={mockDiffContent} onDownload={mockOnDownload} />);
    
    const downloadButton = screen.getByText('Download');
    fireEvent.click(downloadButton);
    
    expect(mockOnDownload).toHaveBeenCalled();
  });

  it('should call onCreatePR when create PR button is clicked', () => {
    const mockOnCreatePR = jest.fn();
    render(<DiffViewer patch={mockPatch} onCreatePR={mockOnCreatePR} />);
    
    const createPRButton = screen.getByText('Create PR');
    fireEvent.click(createPRButton);
    
    expect(mockOnCreatePR).toHaveBeenCalled();
  });

  it('should not show create PR button when onCreatePR is not provided', () => {
    render(<DiffViewer patch={mockPatch} />);
    
    expect(screen.queryByText('Create PR')).not.toBeInTheDocument();
  });

  it('should disable buttons when no content is available', () => {
    render(<DiffViewer />);
    
    // All view mode buttons should be disabled
    expect(screen.getByText('Diff')).toBeDisabled();
    expect(screen.getByText('Patch')).toBeDisabled();
    expect(screen.getByText('Preview')).toBeDisabled();
  });

  it('should handle copy error gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    (navigator.clipboard.writeText as jest.Mock).mockRejectedValue(new Error('Copy failed'));
    
    render(<DiffViewer diffContent={mockDiffContent} />);
    
    const copyButton = screen.getByText('Copy');
    fireEvent.click(copyButton);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to copy:', expect.any(Error));
    });
    
    consoleSpy.mockRestore();
  });

  it('should generate sample code when patch is provided but no original/modified code', () => {
    render(<DiffViewer patch={mockPatch} />);
    
    // Should render Monaco diff editor with generated sample code
    expect(screen.getByTestId('monaco-diff-editor')).toBeInTheDocument();
    expect(screen.getByTestId('original-code')).toHaveTextContent('Original code before transformation');
    expect(screen.getByTestId('modified-code')).toHaveTextContent('Modified code after transformation');
  });

  it('should use provided language for Monaco editor', () => {
    render(
      <DiffViewer 
        originalCode="const test = 'typescript';"
        modifiedCode="const test: string = 'typescript';"
        language="typescript"
      />
    );
    
    expect(screen.getByTestId('monaco-diff-editor')).toBeInTheDocument();
  });
});
