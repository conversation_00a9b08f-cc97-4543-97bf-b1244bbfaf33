import { supabaseClient } from './supabase.js';
import crypto from 'crypto';

export interface Secret {
  id: string;
  key_name: string;
  key_type: 'openai' | 'anthropic' | 'github' | 'slack' | 'stripe';
  created_at: string;
  updated_at: string;
  expires_at?: string;
  rotation_count: number;
  last_rotated_at?: string;
  is_active: boolean;
}

export interface SecretValue {
  key_name: string;
  value: string;
}

class SecretsService {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  /**
   * Encrypt a secret value using AES-256-GCM
   */
  private encrypt(text: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, key);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  /**
   * Decrypt a secret value
   */
  private decrypt(encryptedText: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    
    const [ivHex, authTagHex, encrypted] = encryptedText.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(algorithm, key);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Store a new secret
   */
  async storeSecret(keyName: string, value: string, keyType: Secret['key_type']): Promise<Secret> {
    const encryptedValue = this.encrypt(value);
    
    const { data, error } = await supabaseClient
      .from('secrets')
      .insert({
        key_name: keyName,
        encrypted_value: encryptedValue,
        key_type: keyType,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to store secret: ${error.message}`);
    }

    return data;
  }

  /**
   * Retrieve a secret value by key name
   */
  async getSecret(keyName: string): Promise<string | null> {
    const { data, error } = await supabaseClient
      .from('secrets')
      .select('encrypted_value')
      .eq('key_name', keyName)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      return null;
    }

    try {
      return this.decrypt(data.encrypted_value);
    } catch (decryptError) {
      console.error('Failed to decrypt secret:', decryptError);
      return null;
    }
  }

  /**
   * List all secrets (without values)
   */
  async listSecrets(): Promise<Secret[]> {
    const { data, error } = await supabaseClient
      .from('secrets')
      .select('id, key_name, key_type, created_at, updated_at, expires_at, rotation_count, last_rotated_at, is_active')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to list secrets: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Update a secret value
   */
  async updateSecret(keyName: string, newValue: string): Promise<void> {
    const encryptedValue = this.encrypt(newValue);
    
    const { error } = await supabaseClient
      .from('secrets')
      .update({
        encrypted_value: encryptedValue,
        updated_at: new Date().toISOString()
      })
      .eq('key_name', keyName)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to update secret: ${error.message}`);
    }
  }

  /**
   * Delete a secret (mark as inactive)
   */
  async deleteSecret(keyName: string): Promise<void> {
    const { error } = await supabaseClient
      .from('secrets')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('key_name', keyName);

    if (error) {
      throw new Error(`Failed to delete secret: ${error.message}`);
    }
  }

  /**
   * Get secrets that need rotation (≥90 days old)
   */
  async getSecretsForRotation(): Promise<any[]> {
    const { data, error } = await supabaseClient
      .rpc('check_secrets_for_rotation');

    if (error) {
      throw new Error(`Failed to check secrets for rotation: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get API key for a specific service, with fallback to environment variables
   */
  async getApiKey(service: 'openai' | 'anthropic' | 'github'): Promise<string | null> {
    // First try to get from secrets
    const secretKey = await this.getSecret(`${service}_api_key`);
    if (secretKey) {
      return secretKey;
    }

    // Fallback to environment variables
    const envKey = process.env[`${service.toUpperCase()}_API_KEY`];
    if (envKey) {
      // Store in secrets for future use
      await this.storeSecret(`${service}_api_key`, envKey, service);
      return envKey;
    }

    return null;
  }

  /**
   * Initialize default secrets from environment variables
   */
  async initializeFromEnv(): Promise<void> {
    const envSecrets = [
      { key: 'openai_api_key', env: 'OPENAI_API_KEY', type: 'openai' as const },
      { key: 'anthropic_api_key', env: 'ANTHROPIC_API_KEY', type: 'anthropic' as const },
      { key: 'github_token', env: 'GITHUB_TOKEN', type: 'github' as const },
      { key: 'slack_webhook_url', env: 'SLACK_WEBHOOK_URL', type: 'slack' as const }
    ];

    for (const secret of envSecrets) {
      const envValue = process.env[secret.env];
      if (envValue) {
        const existing = await this.getSecret(secret.key);
        if (!existing) {
          await this.storeSecret(secret.key, envValue, secret.type);
          console.log(`Initialized secret: ${secret.key}`);
        }
      }
    }
  }
}

export const secretsService = new SecretsService();
