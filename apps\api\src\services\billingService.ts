import Stripe from 'stripe';
import { supabaseClient } from './supabase.js';
import { loggingService } from './loggingService.js';

export interface SubscriptionPlan {
  id: string;
  name: string;
  stripe_price_id: string;
  stripe_product_id: string;
  amount: number;
  currency: string;
  interval: string;
  interval_count: number;
  transformations_limit: number | null;
  features: string[];
  is_active: boolean;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  stripe_subscription_id: string;
  stripe_customer_id: string;
  plan_id: string;
  status: string;
  current_period_start: string;
  current_period_end: string;
  trial_start?: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
  canceled_at?: string;
}

export interface UsageRecord {
  id: string;
  user_id: string;
  subscription_id: string;
  session_id?: string;
  usage_type: string;
  quantity: number;
  metadata: any;
  recorded_at: string;
  billing_period_start: string;
  billing_period_end: string;
}

class BillingService {
  private stripe: Stripe;

  constructor() {
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    
    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2024-06-20'
    });
  }

  /**
   * Get all available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data, error } = await supabaseClient
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('amount', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch subscription plans: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to get subscription plans',
        service: 'billing',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      throw error;
    }
  }

  /**
   * Create a Stripe customer for a user
   */
  async createCustomer(userId: string, email: string, name?: string): Promise<string> {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          user_id: userId
        }
      });

      await loggingService.log({
        level: 'info',
        message: 'Stripe customer created',
        service: 'billing',
        user_id: userId,
        metadata: {
          stripe_customer_id: customer.id,
          email
        }
      });

      return customer.id;
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to create Stripe customer',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          email
        }
      });
      throw error;
    }
  }

  /**
   * Create a subscription for a user
   */
  async createSubscription(
    userId: string, 
    customerId: string, 
    priceId: string,
    trialDays?: number
  ): Promise<UserSubscription> {
    try {
      // Get the plan details
      const { data: plan, error: planError } = await supabaseClient
        .from('subscription_plans')
        .select('*')
        .eq('stripe_price_id', priceId)
        .single();

      if (planError || !plan) {
        throw new Error(`Invalid price ID: ${priceId}`);
      }

      // Create Stripe subscription
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        items: [{ price: priceId }],
        metadata: {
          user_id: userId,
          plan_id: plan.id
        }
      };

      if (trialDays && trialDays > 0) {
        subscriptionParams.trial_period_days = trialDays;
      }

      const stripeSubscription = await this.stripe.subscriptions.create(subscriptionParams);

      // Save subscription to database
      const subscriptionData = {
        user_id: userId,
        stripe_subscription_id: stripeSubscription.id,
        stripe_customer_id: customerId,
        plan_id: plan.id,
        status: stripeSubscription.status,
        current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
        trial_start: stripeSubscription.trial_start 
          ? new Date(stripeSubscription.trial_start * 1000).toISOString() 
          : null,
        trial_end: stripeSubscription.trial_end 
          ? new Date(stripeSubscription.trial_end * 1000).toISOString() 
          : null,
        cancel_at_period_end: stripeSubscription.cancel_at_period_end
      };

      const { data: subscription, error } = await supabaseClient
        .from('subscriptions')
        .insert(subscriptionData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to save subscription: ${error.message}`);
      }

      await loggingService.log({
        level: 'info',
        message: 'Subscription created successfully',
        service: 'billing',
        user_id: userId,
        metadata: {
          subscription_id: subscription.id,
          stripe_subscription_id: stripeSubscription.id,
          plan_name: plan.name,
          trial_days: trialDays
        }
      });

      return subscription;
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to create subscription',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          price_id: priceId
        }
      });
      throw error;
    }
  }

  /**
   * Get user's current subscription
   */
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const { data, error } = await supabaseClient
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw new Error(`Failed to get user subscription: ${error.message}`);
      }

      return data || null;
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to get user subscription',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      throw error;
    }
  }

  /**
   * Record usage for billing
   */
  async recordUsage(
    userId: string,
    usageType: string,
    quantity: number = 1,
    sessionId?: string,
    metadata: any = {}
  ): Promise<void> {
    try {
      // Get user's current subscription
      const subscription = await this.getUserSubscription(userId);
      
      if (!subscription) {
        // User has no subscription, might be on free tier
        await loggingService.log({
          level: 'warn',
          message: 'Usage recorded for user without subscription',
          service: 'billing',
          user_id: userId,
          metadata: {
            usage_type: usageType,
            quantity
          }
        });
        return;
      }

      // Calculate billing period
      const periodStart = new Date(subscription.current_period_start);
      const periodEnd = new Date(subscription.current_period_end);

      const usageRecord = {
        user_id: userId,
        subscription_id: subscription.id,
        session_id: sessionId,
        usage_type: usageType,
        quantity,
        metadata,
        billing_period_start: periodStart.toISOString(),
        billing_period_end: periodEnd.toISOString()
      };

      const { error } = await supabaseClient
        .from('usage_records')
        .insert(usageRecord);

      if (error) {
        throw new Error(`Failed to record usage: ${error.message}`);
      }

      await loggingService.log({
        level: 'info',
        message: 'Usage recorded successfully',
        service: 'billing',
        user_id: userId,
        metadata: {
          usage_type: usageType,
          quantity,
          session_id: sessionId
        }
      });
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to record usage',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          usage_type: usageType,
          quantity
        }
      });
      throw error;
    }
  }

  /**
   * Get usage summary for current billing period
   */
  async getUsageSummary(userId: string): Promise<{
    current_period: { start: string; end: string };
    usage_by_type: Record<string, number>;
    total_usage: number;
    limit: number | null;
    remaining: number | null;
  }> {
    try {
      const subscription = await this.getUserSubscription(userId);
      
      if (!subscription) {
        return {
          current_period: { start: '', end: '' },
          usage_by_type: {},
          total_usage: 0,
          limit: null,
          remaining: null
        };
      }

      // Get usage for current billing period
      const { data: usageRecords, error } = await supabaseClient
        .from('usage_records')
        .select('usage_type, quantity')
        .eq('user_id', userId)
        .eq('subscription_id', subscription.id)
        .gte('recorded_at', subscription.current_period_start)
        .lte('recorded_at', subscription.current_period_end);

      if (error) {
        throw new Error(`Failed to get usage summary: ${error.message}`);
      }

      // Aggregate usage by type
      const usageByType: Record<string, number> = {};
      let totalUsage = 0;

      (usageRecords || []).forEach(record => {
        usageByType[record.usage_type] = (usageByType[record.usage_type] || 0) + record.quantity;
        totalUsage += record.quantity;
      });

      // Get plan limits
      const { data: plan } = await supabaseClient
        .from('subscription_plans')
        .select('transformations_limit')
        .eq('id', subscription.plan_id)
        .single();

      const limit = plan?.transformations_limit || null;
      const remaining = limit ? Math.max(0, limit - totalUsage) : null;

      return {
        current_period: {
          start: subscription.current_period_start,
          end: subscription.current_period_end
        },
        usage_by_type: usageByType,
        total_usage: totalUsage,
        limit,
        remaining
      };
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to get usage summary',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      throw error;
    }
  }

  /**
   * Check if user has exceeded usage limits
   */
  async checkUsageLimit(userId: string, usageType: string = 'transformation'): Promise<{
    allowed: boolean;
    limit: number | null;
    current: number;
    remaining: number | null;
  }> {
    try {
      const summary = await this.getUsageSummary(userId);
      const currentUsage = summary.usage_by_type[usageType] || 0;
      
      if (summary.limit === null) {
        // Unlimited plan
        return {
          allowed: true,
          limit: null,
          current: currentUsage,
          remaining: null
        };
      }

      const allowed = currentUsage < summary.limit;
      const remaining = Math.max(0, summary.limit - currentUsage);

      return {
        allowed,
        limit: summary.limit,
        current: currentUsage,
        remaining
      };
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to check usage limit',
        service: 'billing',
        user_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          usage_type: usageType
        }
      });
      
      // Default to allowing usage if check fails
      return {
        allowed: true,
        limit: null,
        current: 0,
        remaining: null
      };
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhook(event: Stripe.Event): Promise<void> {
    try {
      await loggingService.log({
        level: 'info',
        message: 'Processing Stripe webhook',
        service: 'billing',
        metadata: {
          event_type: event.type,
          event_id: event.id
        }
      });

      switch (event.type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdate(event.data.object as Stripe.Subscription);
          break;
          
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
          
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
          
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
          
        default:
          await loggingService.log({
            level: 'info',
            message: 'Unhandled webhook event type',
            service: 'billing',
            metadata: {
              event_type: event.type
            }
          });
      }
    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to handle webhook',
        service: 'billing',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          event_type: event.type,
          event_id: event.id
        }
      });
      throw error;
    }
  }

  private async handleSubscriptionUpdate(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata.user_id;
    
    if (!userId) {
      throw new Error('No user_id in subscription metadata');
    }

    const updateData = {
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      trial_start: subscription.trial_start 
        ? new Date(subscription.trial_start * 1000).toISOString() 
        : null,
      trial_end: subscription.trial_end 
        ? new Date(subscription.trial_end * 1000).toISOString() 
        : null,
      cancel_at_period_end: subscription.cancel_at_period_end,
      canceled_at: subscription.canceled_at 
        ? new Date(subscription.canceled_at * 1000).toISOString() 
        : null
    };

    const { error } = await supabaseClient
      .from('subscriptions')
      .update(updateData)
      .eq('stripe_subscription_id', subscription.id);

    if (error) {
      throw new Error(`Failed to update subscription: ${error.message}`);
    }
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const { error } = await supabaseClient
      .from('subscriptions')
      .update({ 
        status: 'canceled',
        canceled_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', subscription.id);

    if (error) {
      throw new Error(`Failed to mark subscription as deleted: ${error.message}`);
    }
  }

  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    // Update invoice status and record payment
    await loggingService.log({
      level: 'info',
      message: 'Payment succeeded',
      service: 'billing',
      metadata: {
        invoice_id: invoice.id,
        amount: invoice.amount_paid,
        customer_id: invoice.customer
      }
    });
  }

  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    // Handle failed payment - could trigger notifications, etc.
    await loggingService.log({
      level: 'warn',
      message: 'Payment failed',
      service: 'billing',
      metadata: {
        invoice_id: invoice.id,
        amount: invoice.amount_due,
        customer_id: invoice.customer
      }
    });
  }
}

export const billingService = new BillingService();
