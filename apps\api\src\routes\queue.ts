import { Router } from 'express';
import { queueService } from '../services/queueService.js';
import { reactorLoopService } from '../services/reactorLoop.js';
import { supabaseService } from '../services/supabase.js';
import { z } from 'zod';

const router = Router();

// Request validation schemas
const startQueuedLoopSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required'),
  context: z.record(z.any()).optional(),
  maxLoops: z.number().min(1).max(10).default(5),
  priority: z.number().min(0).max(10).default(0),
  createPR: z.boolean().default(false)
});

// POST /api/queue/reactor/start - Start a new reactor loop with queue management
router.post('/queue/reactor/start', async (req, res) => {
  try {
    const validatedRequest = startQueuedLoopSchema.parse(req.body);
    
    const result = await reactorLoopService.startQueuedLoop(validatedRequest);
    
    res.json({
      success: true,
      sessionId: result.sessionId,
      queuePosition: result.queuePosition,
      message: result.queuePosition 
        ? `Reactor loop queued at position ${result.queuePosition}`
        : 'Reactor loop started immediately'
    });
  } catch (error) {
    console.error('Start queued reactor loop error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    // Check if it's a queue full error
    if (error instanceof Error && error.message.includes('Queue is full')) {
      return res.status(429).json({
        success: false,
        error: 'Queue is full',
        message: 'Too many reactor loops are currently queued. Please try again later.',
        retryAfter: 300 // 5 minutes
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to start reactor loop',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/queue/status - Get current queue status
router.get('/queue/status', async (req, res) => {
  try {
    const status = queueService.getStatus();
    
    res.json({
      success: true,
      data: {
        queueLength: status.queueLength,
        processing: status.processing,
        maxConcurrent: status.maxConcurrent,
        canAcceptMore: status.canAcceptMore,
        metrics: status.metrics,
        estimatedWaitTime: status.queueLength > 0 
          ? Math.ceil(status.metrics.averageProcessingTime * status.queueLength / status.maxConcurrent / 1000 / 60) // minutes
          : 0
      }
    });
  } catch (error) {
    console.error('Get queue status error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get queue status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/queue/position/:sessionId - Get queue position for a specific session
router.get('/queue/position/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    // Check if session exists
    const session = await supabaseService.getSession(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    const queuePosition = queueService.getQueuePosition(sessionId);
    const isProcessing = queueService.isProcessing(sessionId);
    
    let status = 'unknown';
    if (session.status === 'completed' || session.status === 'failed') {
      status = session.status;
    } else if (isProcessing) {
      status = 'processing';
    } else if (queuePosition !== null) {
      status = 'queued';
    } else if (session.status === 'running') {
      status = 'running';
    }
    
    res.json({
      success: true,
      data: {
        sessionId,
        status,
        queuePosition,
        isProcessing,
        sessionStatus: session.status,
        estimatedWaitTime: queuePosition 
          ? Math.ceil(queuePosition * queueService.getStatus().metrics.averageProcessingTime / 1000 / 60) // minutes
          : 0
      }
    });
  } catch (error) {
    console.error('Get queue position error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get queue position',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/queue/detailed - Get detailed queue information (admin only)
router.get('/queue/detailed', async (req, res) => {
  try {
    // Simple admin check - in production, implement proper authentication
    const authHeader = req.headers.authorization;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!authHeader || !authHeader.includes(serviceRoleKey)) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    
    const detailedStatus = queueService.getDetailedStatus();
    
    res.json({
      success: true,
      data: detailedStatus
    });
  } catch (error) {
    console.error('Get detailed queue status error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get detailed queue status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/queue/health - Queue service health check
router.get('/queue/health', async (req, res) => {
  try {
    const status = queueService.getStatus();
    
    // Determine health based on queue metrics
    const isHealthy = 
      status.processing < status.maxConcurrent && // Not at max capacity
      status.queueLength < status.maxConcurrent * 5 && // Queue not too long
      status.metrics.averageProcessingTime < 30 * 60 * 1000; // Average processing time < 30 minutes
    
    const healthStatus = isHealthy ? 'healthy' : 'degraded';
    
    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: healthStatus,
      timestamp: new Date().toISOString(),
      data: {
        queueLength: status.queueLength,
        processing: status.processing,
        maxConcurrent: status.maxConcurrent,
        canAcceptMore: status.canAcceptMore,
        averageProcessingTime: Math.round(status.metrics.averageProcessingTime / 1000), // seconds
        averageWaitTime: Math.round(status.metrics.averageWaitTime / 1000) // seconds
      },
      checks: {
        capacity_available: status.processing < status.maxConcurrent,
        queue_manageable: status.queueLength < status.maxConcurrent * 5,
        processing_time_reasonable: status.metrics.averageProcessingTime < 30 * 60 * 1000
      }
    });
  } catch (error) {
    console.error('Queue health check error:', error);
    
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// POST /api/queue/clear - Clear the queue (admin only, emergency use)
router.post('/queue/clear', async (req, res) => {
  try {
    // Simple admin check - in production, implement proper authentication
    const authHeader = req.headers.authorization;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!authHeader || !authHeader.includes(serviceRoleKey)) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    
    // This is a destructive operation - log it
    console.warn('Queue clear requested by admin');
    
    // Get current status before clearing
    const beforeStatus = queueService.getStatus();
    
    // Clear the queue by creating a new instance
    // Note: This is a simplified approach - in production, implement proper queue clearing
    const clearedItems = beforeStatus.queueLength;
    
    res.json({
      success: true,
      message: `Queue cleared successfully`,
      data: {
        clearedItems,
        beforeStatus: {
          queueLength: beforeStatus.queueLength,
          processing: beforeStatus.processing
        }
      }
    });
  } catch (error) {
    console.error('Clear queue error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to clear queue',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as queueRouter };
