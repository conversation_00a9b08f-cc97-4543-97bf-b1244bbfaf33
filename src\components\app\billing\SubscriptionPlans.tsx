import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Check, 
  Zap, 
  Crown, 
  Rocket,
  AlertCircle,
  CreditCard,
  Users,
  Clock
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: PlanFeature[];
  popular?: boolean;
  current?: boolean;
  stripePriceId: string;
}

interface SubscriptionPlansProps {
  onPlanSelect?: (plan: SubscriptionPlan) => void;
  currentPlanId?: string;
}

export const SubscriptionPlans = ({ onPlanSelect, currentPlanId }: SubscriptionPlansProps) => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscribingTo, setSubscribingTo] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchPlans = async () => {
    try {
      setError(null);
      const response = await fetch('/api/billing/plans');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch plans: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch subscription plans');
      }
      
      setPlans(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Plans Loading Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    if (plan.current) {
      toast({
        title: 'Already Subscribed',
        description: 'You are already on this plan.',
      });
      return;
    }

    setSubscribingTo(plan.id);

    try {
      const response = await fetch('/api/billing/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase_token')}`,
        },
        body: JSON.stringify({
          price_id: plan.stripePriceId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create subscription: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Redirect to Stripe Checkout or handle success
        if (result.data.checkout_url) {
          window.location.href = result.data.checkout_url;
        } else {
          toast({
            title: 'Subscription Created',
            description: 'Your subscription has been activated!',
          });
          onPlanSelect?.(plan);
        }
      } else {
        throw new Error(result.error || 'Failed to create subscription');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast({
        title: 'Subscription Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setSubscribingTo(null);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const getPlanIcon = (planName: string) => {
    const name = planName.toLowerCase();
    if (name.includes('free') || name.includes('starter')) return <Zap className="h-6 w-6" />;
    if (name.includes('pro') || name.includes('professional')) return <Crown className="h-6 w-6" />;
    if (name.includes('enterprise') || name.includes('team')) return <Rocket className="h-6 w-6" />;
    return <Users className="h-6 w-6" />;
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-8 w-20" />
              <div className="space-y-2">
                {[...Array(5)].map((_, j) => (
                  <Skeleton key={j} className="h-4 w-full" />
                ))}
              </div>
              <Skeleton className="h-10 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchPlans}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Plan</h2>
        <p className="text-muted-foreground">
          Select the perfect plan for your code transformation needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card 
            key={plan.id} 
            className={`relative ${
              plan.popular ? 'border-purple-500 shadow-lg' : ''
            } ${
              plan.current ? 'border-green-500' : ''
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                <Badge className="bg-purple-600 text-white">
                  Most Popular
                </Badge>
              </div>
            )}
            
            {plan.current && (
              <div className="absolute -top-3 right-4">
                <Badge className="bg-green-600 text-white">
                  Current Plan
                </Badge>
              </div>
            )}

            <CardHeader className="text-center">
              <div className="flex justify-center mb-2">
                {getPlanIcon(plan.name)}
              </div>
              <CardTitle className="text-xl">{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              
              <div className="space-y-1">
                <div className="text-3xl font-bold">
                  ${plan.price}
                  <span className="text-base font-normal text-muted-foreground">
                    /{plan.interval}
                  </span>
                </div>
                {plan.interval === 'year' && (
                  <p className="text-sm text-green-600">
                    Save 20% with annual billing
                  </p>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-3">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Check className={`h-4 w-4 mt-0.5 ${
                      feature.included ? 'text-green-500' : 'text-muted-foreground'
                    }`} />
                    <div className="flex-1">
                      <span className={`text-sm ${
                        feature.included ? 'text-foreground' : 'text-muted-foreground'
                      }`}>
                        {feature.name}
                      </span>
                      {feature.limit && (
                        <span className="text-xs text-muted-foreground ml-1">
                          ({feature.limit})
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <Button
                onClick={() => handleSubscribe(plan)}
                disabled={subscribingTo === plan.id || plan.current}
                className={`w-full ${
                  plan.popular 
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' 
                    : ''
                }`}
                variant={plan.current ? 'outline' : 'default'}
              >
                {subscribingTo === plan.id ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : plan.current ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Current Plan
                  </>
                ) : plan.price === 0 ? (
                  'Get Started Free'
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Subscribe Now
                  </>
                )}
              </Button>

              {plan.price > 0 && !plan.current && (
                <p className="text-xs text-center text-muted-foreground">
                  Cancel anytime. No hidden fees.
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center space-y-2">
        <p className="text-sm text-muted-foreground">
          All plans include secure API key storage, real-time streaming, and 24/7 support.
        </p>
        <p className="text-xs text-muted-foreground">
          Prices shown in USD. Additional usage charges may apply for high-volume usage.
        </p>
      </div>
    </div>
  );
};
