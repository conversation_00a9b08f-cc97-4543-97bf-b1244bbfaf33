module.exports = {
  ci: {
    // Collect configuration
    collect: {
      // URLs to test
      url: [
        'http://localhost:4173',
        'http://localhost:4173/reactor',
        'http://localhost:4173/history',
        'http://localhost:4173/settings',
        'http://localhost:4173/landing',
      ],
      
      // Lighthouse settings
      settings: {
        // Use desktop preset for consistent results
        preset: 'desktop',
        
        // Custom settings
        chromeFlags: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
        ],
        
        // Throttling settings for consistent results
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0,
        },
        
        // Skip certain audits that might be flaky in CI
        skipAudits: [
          'uses-http2',
          'uses-long-cache-ttl',
          'uses-optimized-images',
          'uses-webp-images',
          'uses-text-compression',
          'redirects-http',
        ],
        
        // Only run specific categories
        onlyCategories: [
          'performance',
          'accessibility',
          'best-practices',
          'seo',
        ],
      },
      
      // Number of runs per URL
      numberOfRuns: 3,
      
      // Start server automatically
      startServerCommand: 'npm run preview --workspace=apps/web -- --port 4173',
      startServerReadyPattern: 'Local:.*:4173',
      startServerReadyTimeout: 30000,
    },
    
    // Assert configuration - define performance budgets
    assert: {
      // Global assertions for all URLs
      assertions: {
        // Performance category
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        
        // Core Web Vitals
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 4000 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        'speed-index': ['error', { maxNumericValue: 4000 }],
        
        // Resource budgets
        'resource-summary:document:size': ['error', { maxNumericValue: 50000 }], // 50KB
        'resource-summary:script:size': ['error', { maxNumericValue: 500000 }], // 500KB
        'resource-summary:stylesheet:size': ['error', { maxNumericValue: 100000 }], // 100KB
        'resource-summary:image:size': ['error', { maxNumericValue: 1000000 }], // 1MB
        'resource-summary:font:size': ['error', { maxNumericValue: 200000 }], // 200KB
        
        // Network requests
        'resource-summary:total:count': ['error', { maxNumericValue: 50 }],
        'resource-summary:script:count': ['error', { maxNumericValue: 10 }],
        'resource-summary:stylesheet:count': ['error', { maxNumericValue: 5 }],
        
        // Specific audits
        'unused-css-rules': ['error', { maxNumericValue: 50000 }],
        'unused-javascript': ['error', { maxNumericValue: 100000 }],
        'modern-image-formats': ['warn', { minScore: 0.8 }],
        'offscreen-images': ['warn', { minScore: 0.8 }],
        'render-blocking-resources': ['warn', { minScore: 0.8 }],
        'unminified-css': ['error', { minScore: 1 }],
        'unminified-javascript': ['error', { minScore: 1 }],
        'efficient-animated-content': ['warn', { minScore: 0.8 }],
        'duplicated-javascript': ['error', { minScore: 1 }],
        'legacy-javascript': ['warn', { minScore: 0.8 }],
        
        // Accessibility audits
        'color-contrast': ['error', { minScore: 1 }],
        'image-alt': ['error', { minScore: 1 }],
        'label': ['error', { minScore: 1 }],
        'link-name': ['error', { minScore: 1 }],
        'button-name': ['error', { minScore: 1 }],
        'document-title': ['error', { minScore: 1 }],
        'html-has-lang': ['error', { minScore: 1 }],
        'html-lang-valid': ['error', { minScore: 1 }],
        'meta-description': ['error', { minScore: 1 }],
        'meta-viewport': ['error', { minScore: 1 }],
        
        // Best practices
        'is-on-https': ['error', { minScore: 1 }],
        'uses-https': ['error', { minScore: 1 }],
        'no-vulnerable-libraries': ['error', { minScore: 1 }],
        'csp-xss': ['warn', { minScore: 0.8 }],
        
        // SEO
        'meta-description': ['error', { minScore: 1 }],
        'document-title': ['error', { minScore: 1 }],
        'crawlable-anchors': ['error', { minScore: 1 }],
        'robots-txt': ['warn', { minScore: 0.8 }],
      },
      
      // URL-specific assertions
      preset: 'lighthouse:recommended',
    },
    
    // Upload configuration
    upload: {
      // Upload to Lighthouse CI server (if available)
      target: 'temporary-public-storage',
      
      // GitHub integration
      githubAppToken: process.env.LHCI_GITHUB_APP_TOKEN,
      
      // Custom server configuration (uncomment if using LHCI server)
      // serverBaseUrl: 'https://your-lhci-server.com',
      // token: process.env.LHCI_TOKEN,
    },
    
    // Server configuration (if running LHCI server)
    server: {
      port: 9001,
      storage: {
        storageMethod: 'sql',
        sqlDialect: 'sqlite',
        sqlDatabasePath: './lhci.db',
      },
    },
    
    // Wizard configuration
    wizard: {
      // Skip wizard in CI
      skip: true,
    },
  },
  
  // Custom configuration for different environments
  environments: {
    // Development environment
    development: {
      collect: {
        url: ['http://localhost:5173'],
        settings: {
          preset: 'desktop',
          throttling: {
            rttMs: 0,
            throughputKbps: 0,
            cpuSlowdownMultiplier: 1,
          },
        },
      },
      assert: {
        assertions: {
          'categories:performance': ['warn', { minScore: 0.8 }],
          'categories:accessibility': ['error', { minScore: 0.9 }],
        },
      },
    },
    
    // Staging environment
    staging: {
      collect: {
        url: ['https://staging.metamorphic-reactor.com'],
        settings: {
          preset: 'desktop',
        },
      },
      assert: {
        assertions: {
          'categories:performance': ['error', { minScore: 0.85 }],
          'categories:accessibility': ['error', { minScore: 0.95 }],
        },
      },
    },
    
    // Production environment
    production: {
      collect: {
        url: ['https://metamorphic-reactor.com'],
        settings: {
          preset: 'desktop',
        },
      },
      assert: {
        assertions: {
          'categories:performance': ['error', { minScore: 0.9 }],
          'categories:accessibility': ['error', { minScore: 0.95 }],
          'categories:best-practices': ['error', { minScore: 0.9 }],
          'categories:seo': ['error', { minScore: 0.9 }],
        },
      },
    },
  },
};
