# ZAP Scanning Rules Configuration
# Format: RULE_ID	THRESHOLD	[COMMENT]
# RULE_ID: The ZAP rule identifier
# THRESHOLD: OFF, LOW, MEDIUM, HIGH
# COMMENT: Optional description

# Ignore false positives and adjust thresholds for our application

# Content Security Policy (CSP) Header Not Set
10038	MEDIUM	# We have CSP configured via Helmet

# Missing Anti-clickjacking Header
10020	HIGH	# Critical for preventing clickjacking attacks

# X-Content-Type-Options Header Missing
10021	MEDIUM	# Important for MIME type security

# Information Disclosure - Suspicious Comments
10027	LOW	# Development comments should be removed in production

# Timestamp Disclosure - Unix
10096	LOW	# Timestamps in responses are acceptable for our API

# Hash Disclosure - Mac OSX
10097	LOW	# Not applicable to our deployment

# Cross-Domain Misconfiguration
10098	HIGH	# Important for CORS security

# Weak Authentication Method
10105	HIGH	# Critical for authentication security

# HTTP Parameter Override
10026	MEDIUM	# Parameter pollution protection

# Server Leaks Information via "X-Powered-By" HTTP Response Header Field(s)
10037	LOW	# We remove X-Powered-By header via Helmet

# Secure Pages Include Mixed Content
10040	HIGH	# Important for HTTPS security

# HTTP to HTTPS Insecure Transition in Form Post
10041	HIGH	# Critical for form security

# HTTPS to HTTP Insecure Transition in Form Post
10042	HIGH	# Critical for form security

# User Controllable Charset
10030	MEDIUM	# Character set manipulation

# User Controllable HTML Element Attribute (Potential XSS)
10031	HIGH	# XSS prevention is critical

# Information Disclosure - Sensitive Information in URL
10024	MEDIUM	# URL parameter security

# Information Disclosure - Sensitive Information in HTTP Referrer Header
10025	LOW	# Referrer policy configured

# HTTP Parameter Pollution
10026	MEDIUM	# Parameter validation

# Cookie No HttpOnly Flag
10010	HIGH	# Cookie security is critical

# Cookie Without Secure Flag
10011	HIGH	# HTTPS cookie security

# Incomplete or No Cache-control and Pragma HTTP Header Set
10015	LOW	# Caching headers for static content

# Web Browser XSS Protection Not Enabled
10016	MEDIUM	# XSS protection headers

# Cross-Domain JavaScript Source File Inclusion
10017	MEDIUM	# External script security

# Content-Type Header Missing
10019	LOW	# Content type specification

# Anti-CSRF Tokens Scanner
10023	HIGH	# CSRF protection is critical

# Generic Padding Oracle
10024	HIGH	# Cryptographic security

# Strict-Transport-Security Header Not Set
10035	HIGH	# HTTPS enforcement

# Server Leaks Version Information via "Server" HTTP Response Header Field
10036	LOW	# Server information disclosure

# SQL Injection
40018	HIGH	# Critical database security

# Cross Site Scripting (Reflected)
40012	HIGH	# Critical XSS protection

# Cross Site Scripting (Stored)
40014	HIGH	# Critical XSS protection

# Cross Site Scripting (DOM Based)
40016	HIGH	# Critical XSS protection

# HTML Injection
40013	HIGH	# HTML injection prevention

# LDAP Injection
40015	HIGH	# LDAP security

# Path Traversal
6	HIGH	# Directory traversal protection

# Remote File Inclusion
7	HIGH	# File inclusion security

# Server Side Include
40009	HIGH	# SSI injection prevention

# Cross Site Request Forgery
40016	HIGH	# CSRF protection

# Buffer Overflow
30001	HIGH	# Memory safety

# Format String Error
30002	HIGH	# Format string security

# Integer Overflow Error
30003	HIGH	# Integer overflow protection

# OS Command Injection
90020	HIGH	# Command injection prevention

# Remote OS Command Injection
90021	HIGH	# Remote command execution

# External Redirect
20019	MEDIUM	# Open redirect prevention

# Source Code Disclosure - /WEB-INF folder
10045	HIGH	# Source code protection

# Backup File Disclosure
10095	MEDIUM	# Backup file security

# .htaccess Information Leak
40032	LOW	# Apache configuration

# .env Information Leak
40033	HIGH	# Environment file protection

# Application Error Disclosure
90022	MEDIUM	# Error handling

# Generic Padding Oracle
90024	HIGH	# Cryptographic padding

# Expression Language Injection
90025	HIGH	# EL injection prevention

# SOAP Action Spoofing
90026	MEDIUM	# SOAP security

# Cookie Slack Detector
90027	LOW	# Cookie analysis

# Insecure JSF ViewState
90028	HIGH	# JSF security

# SOAP XML Injection
90029	HIGH	# XML injection in SOAP

# Wappalyzer Technology Detection
90004	LOW	# Technology fingerprinting

# Retire.js
10003	HIGH	# Vulnerable JavaScript libraries

# Modern Web Application
10049	LOW	# Modern app detection

# In Page Banner Information Leak
10009	LOW	# Banner information

# Cookie Poisoning
10029	MEDIUM	# Cookie manipulation

# Suspicious Comment
10027	LOW	# Code comments

# Viewstate Scanner
90032	MEDIUM	# ASP.NET ViewState

# Directory Browsing
0	HIGH	# Directory listing prevention

# Heartbleed OpenSSL Vulnerability
10034	HIGH	# SSL/TLS security

# POODLE
10035	HIGH	# SSL/TLS vulnerability

# Sweet32
10036	HIGH	# SSL/TLS cipher security
