import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Github, ExternalLink, Loader2, CheckCircle, AlertCircle, Unlink } from 'lucide-react';
import { toast } from 'sonner';
import { githubService, GitHubUser, GitHubRepo } from '@/lib/github';

interface GitHubConnectionProps {
  onConnectionChange?: (connected: boolean) => void;
  onRepoSelect?: (repo: GitHubRepo | null) => void;
  showRepoSelector?: boolean;
}

export const GitHubConnection = ({ 
  onConnectionChange, 
  onRepoSelect, 
  showRepoSelector = false 
}: GitHubConnectionProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<GitHubUser | null>(null);
  const [repos, setRepos] = useState<GitHubRepo[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<GitHubRepo | null>(null);
  const [loadingRepos, setLoadingRepos] = useState(false);

  useEffect(() => {
    checkConnection();
  }, []);

  const checkConnection = async () => {
    try {
      const token = await githubService.getStoredToken();
      if (token) {
        const userInfo = await githubService.getCurrentUser();
        setUser(userInfo);
        setIsConnected(true);
        onConnectionChange?.(true);

        if (showRepoSelector) {
          await loadRepos();
        }
      }
    } catch (error) {
      console.error('Error checking GitHub connection:', error);
      setIsConnected(false);
      onConnectionChange?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRepos = async () => {
    setLoadingRepos(true);
    try {
      const userRepos = await githubService.getUserRepos();
      setRepos(userRepos);
    } catch (error) {
      console.error('Error loading repos:', error);
      toast.error('Failed to load repositories');
    } finally {
      setLoadingRepos(false);
    }
  };

  const handleConnect = () => {
    setIsConnecting(true);
    const oauthUrl = githubService.getOAuthUrl();
    
    // Open OAuth popup
    const popup = window.open(
      oauthUrl,
      'github-oauth',
      'width=600,height=700,scrollbars=yes,resizable=yes'
    );

    // Listen for popup messages
    const handleMessage = async (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;

      if (event.data.type === 'GITHUB_OAUTH_SUCCESS') {
        const { code, state } = event.data;
        popup?.close();

        try {
          const userInfo = await githubService.handleOAuthCallback(code, state);
          setUser(userInfo);
          setIsConnected(true);
          onConnectionChange?.(true);
          toast.success(`Connected to GitHub as ${userInfo.login}`);

          if (showRepoSelector) {
            await loadRepos();
          }
        } catch (error) {
          console.error('OAuth callback error:', error);
          toast.error('Failed to connect to GitHub');
        } finally {
          setIsConnecting(false);
        }
      } else if (event.data.type === 'GITHUB_OAUTH_ERROR') {
        popup?.close();
        toast.error('GitHub connection cancelled or failed');
        setIsConnecting(false);
      }
    };

    window.addEventListener('message', handleMessage);

    // Check if popup was closed manually
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
        setIsConnecting(false);
      }
    }, 1000);
  };

  const handleDisconnect = async () => {
    try {
      await githubService.removeToken();
      setUser(null);
      setIsConnected(false);
      setRepos([]);
      setSelectedRepo(null);
      onConnectionChange?.(false);
      onRepoSelect?.(null);
      toast.success('Disconnected from GitHub');
    } catch (error) {
      console.error('Error disconnecting from GitHub:', error);
      toast.error('Failed to disconnect from GitHub');
    }
  };

  const handleRepoSelect = (repoFullName: string) => {
    const repo = repos.find(r => r.full_name === repoFullName);
    setSelectedRepo(repo || null);
    onRepoSelect?.(repo || null);
  };

  if (isLoading) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin text-indigo-400" />
            <span className="text-slate-300">Checking GitHub connection...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Github className="w-5 h-5" />
          <span>GitHub Integration</span>
          {isConnected && (
            <Badge className="bg-green-600/20 text-green-300 border-green-500/30">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="text-slate-400">
          Connect your GitHub account to create pull requests automatically
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isConnected ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-slate-400">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Not connected to GitHub</span>
            </div>
            <Button
              onClick={handleConnect}
              disabled={isConnecting}
              className="w-full bg-slate-700 hover:bg-slate-600 text-white border-slate-600"
            >
              {isConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Github className="w-4 h-4 mr-2" />
                  Connect GitHub Account
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* User Info */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <img
                  src={user?.avatar_url}
                  alt={user?.login}
                  className="w-8 h-8 rounded-full"
                />
                <div>
                  <p className="text-white font-medium">{user?.name || user?.login}</p>
                  <p className="text-slate-400 text-sm">@{user?.login}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDisconnect}
                className="text-slate-400 hover:text-white"
              >
                <Unlink className="w-4 h-4 mr-1" />
                Disconnect
              </Button>
            </div>

            {/* Repository Selector */}
            {showRepoSelector && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">
                  Select Repository
                </label>
                {loadingRepos ? (
                  <div className="flex items-center space-x-2 text-slate-400">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Loading repositories...</span>
                  </div>
                ) : (
                  <Select
                    value={selectedRepo?.full_name || ''}
                    onValueChange={handleRepoSelect}
                  >
                    <SelectTrigger className="bg-slate-900 border-slate-700 text-white">
                      <SelectValue placeholder="Choose a repository" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900 border-slate-700">
                      {repos.map((repo) => (
                        <SelectItem key={repo.id} value={repo.full_name}>
                          <div className="flex items-center space-x-2">
                            <span>{repo.name}</span>
                            {repo.permissions.admin && (
                              <Badge variant="outline" className="text-xs">
                                Admin
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                {selectedRepo && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-400">
                      Default branch: <code className="text-slate-300">{selectedRepo.default_branch}</code>
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`https://github.com/${selectedRepo.full_name}`, '_blank')}
                      className="text-slate-400 hover:text-white"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      View on GitHub
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
