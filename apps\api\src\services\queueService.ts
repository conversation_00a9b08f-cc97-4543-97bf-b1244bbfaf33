import { EventEmitter } from 'events';
import { loggingService } from './loggingService.js';

export interface QueueItem {
  id: string;
  sessionId: string;
  priority: number;
  createdAt: Date;
  retryCount: number;
  maxRetries: number;
  data: any;
}

export interface QueueMetrics {
  totalQueued: number;
  processing: number;
  completed: number;
  failed: number;
  averageWaitTime: number;
  averageProcessingTime: number;
}

class QueueService extends EventEmitter {
  private queue: QueueItem[] = [];
  private processing: Map<string, QueueItem> = new Map();
  private completed: Map<string, { item: QueueItem; completedAt: Date }> = new Map();
  private failed: Map<string, { item: QueueItem; failedAt: Date; error: string }> = new Map();
  
  private maxConcurrent: number;
  private maxQueueSize: number;
  private processingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  
  // Metrics
  private metrics: QueueMetrics = {
    totalQueued: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    averageWaitTime: 0,
    averageProcessingTime: 0
  };

  constructor(maxConcurrent: number = 10, maxQueueSize: number = 100) {
    super();
    this.maxConcurrent = maxConcurrent;
    this.maxQueueSize = maxQueueSize;
    
    // Clean up old completed/failed items every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Add an item to the queue
   */
  async enqueue(
    sessionId: string, 
    data: any, 
    priority: number = 0, 
    maxRetries: number = 3
  ): Promise<{ success: boolean; queuePosition?: number; error?: string }> {
    
    // Check if queue is full
    if (this.queue.length >= this.maxQueueSize) {
      await loggingService.log({
        level: 'warn',
        message: 'Queue is full, rejecting new item',
        service: 'queue-service',
        metadata: {
          sessionId,
          queueSize: this.queue.length,
          maxQueueSize: this.maxQueueSize,
          processing: this.processing.size
        }
      });
      
      return {
        success: false,
        error: 'Queue is full. Please try again later.'
      };
    }

    const queueItem: QueueItem = {
      id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sessionId,
      priority,
      createdAt: new Date(),
      retryCount: 0,
      maxRetries,
      data
    };

    // Insert item based on priority (higher priority first)
    let insertIndex = this.queue.length;
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < priority) {
        insertIndex = i;
        break;
      }
    }
    
    this.queue.splice(insertIndex, 0, queueItem);
    this.metrics.totalQueued++;

    await loggingService.log({
      level: 'info',
      message: 'Item added to queue',
      service: 'queue-service',
      metadata: {
        queueItemId: queueItem.id,
        sessionId,
        priority,
        queuePosition: insertIndex + 1,
        queueSize: this.queue.length
      }
    });

    // Try to process immediately if we have capacity
    this.processNext();

    return {
      success: true,
      queuePosition: insertIndex + 1
    };
  }

  /**
   * Process the next item in the queue
   */
  private async processNext(): Promise<void> {
    // Check if we have capacity and items to process
    if (this.processing.size >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    const item = this.queue.shift();
    if (!item) return;

    // Move to processing
    this.processing.set(item.id, item);
    this.metrics.processing = this.processing.size;

    const waitTime = Date.now() - item.createdAt.getTime();
    this.updateAverageWaitTime(waitTime);

    await loggingService.log({
      level: 'info',
      message: 'Started processing queue item',
      service: 'queue-service',
      metadata: {
        queueItemId: item.id,
        sessionId: item.sessionId,
        waitTime,
        processingCount: this.processing.size
      }
    });

    // Set processing timeout (30 minutes max)
    const timeout = setTimeout(() => {
      this.handleTimeout(item.id);
    }, 30 * 60 * 1000);
    
    this.processingTimeouts.set(item.id, timeout);

    // Emit processing event
    this.emit('processing', item);

    // Try to process more items if we still have capacity
    setImmediate(() => this.processNext());
  }

  /**
   * Mark an item as completed
   */
  async markCompleted(itemId: string, result?: any): Promise<void> {
    const item = this.processing.get(itemId);
    if (!item) {
      await loggingService.log({
        level: 'warn',
        message: 'Attempted to mark non-existent item as completed',
        service: 'queue-service',
        metadata: { itemId }
      });
      return;
    }

    // Clear timeout
    const timeout = this.processingTimeouts.get(itemId);
    if (timeout) {
      clearTimeout(timeout);
      this.processingTimeouts.delete(itemId);
    }

    // Move from processing to completed
    this.processing.delete(itemId);
    this.completed.set(itemId, { item, completedAt: new Date() });
    
    this.metrics.processing = this.processing.size;
    this.metrics.completed++;

    const processingTime = Date.now() - item.createdAt.getTime();
    this.updateAverageProcessingTime(processingTime);

    await loggingService.log({
      level: 'info',
      message: 'Queue item completed successfully',
      service: 'queue-service',
      metadata: {
        queueItemId: itemId,
        sessionId: item.sessionId,
        processingTime,
        result: result ? 'success' : 'unknown'
      }
    });

    // Emit completion event
    this.emit('completed', item, result);

    // Process next item
    this.processNext();
  }

  /**
   * Mark an item as failed
   */
  async markFailed(itemId: string, error: string, retry: boolean = true): Promise<void> {
    const item = this.processing.get(itemId);
    if (!item) {
      await loggingService.log({
        level: 'warn',
        message: 'Attempted to mark non-existent item as failed',
        service: 'queue-service',
        metadata: { itemId, error }
      });
      return;
    }

    // Clear timeout
    const timeout = this.processingTimeouts.get(itemId);
    if (timeout) {
      clearTimeout(timeout);
      this.processingTimeouts.delete(itemId);
    }

    // Remove from processing
    this.processing.delete(itemId);
    this.metrics.processing = this.processing.size;

    // Check if we should retry
    if (retry && item.retryCount < item.maxRetries) {
      item.retryCount++;
      
      await loggingService.log({
        level: 'warn',
        message: 'Queue item failed, retrying',
        service: 'queue-service',
        metadata: {
          queueItemId: itemId,
          sessionId: item.sessionId,
          retryCount: item.retryCount,
          maxRetries: item.maxRetries,
          error
        }
      });

      // Add back to queue with lower priority
      item.priority = Math.max(0, item.priority - 1);
      this.queue.push(item);
      
      // Process next item
      this.processNext();
    } else {
      // Move to failed
      this.failed.set(itemId, { item, failedAt: new Date(), error });
      this.metrics.failed++;

      await loggingService.log({
        level: 'error',
        message: 'Queue item failed permanently',
        service: 'queue-service',
        metadata: {
          queueItemId: itemId,
          sessionId: item.sessionId,
          retryCount: item.retryCount,
          maxRetries: item.maxRetries,
          error
        }
      });

      // Emit failure event
      this.emit('failed', item, error);

      // Process next item
      this.processNext();
    }
  }

  /**
   * Handle processing timeout
   */
  private async handleTimeout(itemId: string): Promise<void> {
    await this.markFailed(itemId, 'Processing timeout (30 minutes)', false);
  }

  /**
   * Get current queue status
   */
  getStatus(): {
    queueLength: number;
    processing: number;
    maxConcurrent: number;
    canAcceptMore: boolean;
    metrics: QueueMetrics;
  } {
    return {
      queueLength: this.queue.length,
      processing: this.processing.size,
      maxConcurrent: this.maxConcurrent,
      canAcceptMore: this.queue.length < this.maxQueueSize,
      metrics: { ...this.metrics }
    };
  }

  /**
   * Get queue position for a session
   */
  getQueuePosition(sessionId: string): number | null {
    const index = this.queue.findIndex(item => item.sessionId === sessionId);
    return index >= 0 ? index + 1 : null;
  }

  /**
   * Check if a session is being processed
   */
  isProcessing(sessionId: string): boolean {
    return Array.from(this.processing.values()).some(item => item.sessionId === sessionId);
  }

  /**
   * Update average wait time
   */
  private updateAverageWaitTime(waitTime: number): void {
    const totalProcessed = this.metrics.completed + this.metrics.failed;
    if (totalProcessed === 0) {
      this.metrics.averageWaitTime = waitTime;
    } else {
      this.metrics.averageWaitTime = 
        (this.metrics.averageWaitTime * totalProcessed + waitTime) / (totalProcessed + 1);
    }
  }

  /**
   * Update average processing time
   */
  private updateAverageProcessingTime(processingTime: number): void {
    if (this.metrics.completed === 0) {
      this.metrics.averageProcessingTime = processingTime;
    } else {
      this.metrics.averageProcessingTime = 
        (this.metrics.averageProcessingTime * (this.metrics.completed - 1) + processingTime) / this.metrics.completed;
    }
  }

  /**
   * Clean up old completed and failed items
   */
  private cleanup(): void {
    const cutoffTime = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
    
    // Clean up completed items
    for (const [id, { completedAt }] of this.completed.entries()) {
      if (completedAt < cutoffTime) {
        this.completed.delete(id);
      }
    }
    
    // Clean up failed items
    for (const [id, { failedAt }] of this.failed.entries()) {
      if (failedAt < cutoffTime) {
        this.failed.delete(id);
      }
    }
  }

  /**
   * Get detailed queue information for debugging
   */
  getDetailedStatus(): any {
    return {
      queue: this.queue.map(item => ({
        id: item.id,
        sessionId: item.sessionId,
        priority: item.priority,
        createdAt: item.createdAt,
        retryCount: item.retryCount
      })),
      processing: Array.from(this.processing.values()).map(item => ({
        id: item.id,
        sessionId: item.sessionId,
        startedAt: item.createdAt
      })),
      recentCompleted: Array.from(this.completed.entries()).slice(-10).map(([id, { item, completedAt }]) => ({
        id,
        sessionId: item.sessionId,
        completedAt
      })),
      recentFailed: Array.from(this.failed.entries()).slice(-10).map(([id, { item, failedAt, error }]) => ({
        id,
        sessionId: item.sessionId,
        failedAt,
        error
      })),
      metrics: this.metrics
    };
  }
}

// Create singleton instance
export const queueService = new QueueService(
  parseInt(process.env.MAX_CONCURRENT_LOOPS || '10'),
  parseInt(process.env.MAX_QUEUE_SIZE || '100')
);
