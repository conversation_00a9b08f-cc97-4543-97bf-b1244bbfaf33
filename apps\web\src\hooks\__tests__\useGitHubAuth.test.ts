import { renderHook, act } from '@testing-library/react';
import { useGitHubAuth } from '../useGitHubAuth';
import { supabase } from '@/lib/supabase';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
    },
    from: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

// Mock environment variables
const mockEnv = {
  VITE_GITHUB_CLIENT_ID: 'test-client-id',
};

Object.defineProperty(import.meta, 'env', {
  value: mockEnv,
  writable: true,
});

// Mock window.location
delete (window as any).location;
window.location = { href: '', origin: 'http://localhost:3000' } as any;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('useGitHubAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful session
    mockSupabase.auth.getSession.mockResolvedValue({
      data: {
        session: {
          access_token: 'mock-token',
          user: { id: 'user-123' },
        },
      },
      error: null,
    });
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useGitHubAuth());

    expect(result.current.isConnected).toBe(false);
    expect(result.current.user).toBeNull();
    expect(result.current.isLoading).toBe(true);
  });

  it('should check connection successfully', async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        hasToken: true,
        user: {
          login: 'testuser',
          name: 'Test User',
          avatar_url: 'https://github.com/avatar.jpg',
        },
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      await result.current.checkConnection();
    });

    expect(result.current.isConnected).toBe(true);
    expect(result.current.user).toEqual({
      login: 'testuser',
      name: 'Test User',
      avatar_url: 'https://github.com/avatar.jpg',
    });
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle no session', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      await result.current.checkConnection();
    });

    expect(result.current.isConnected).toBe(false);
    expect(result.current.user).toBeNull();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle API error during connection check', async () => {
    const mockResponse = {
      ok: false,
      json: jest.fn().mockResolvedValue({
        error: 'API Error',
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      await result.current.checkConnection();
    });

    expect(result.current.isConnected).toBe(false);
    expect(result.current.user).toBeNull();
    expect(result.current.isLoading).toBe(false);
  });

  it('should initiate GitHub OAuth flow', () => {
    const { result } = renderHook(() => useGitHubAuth());

    act(() => {
      result.current.connectGitHub();
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'github_oauth_state',
      expect.any(String)
    );
    expect(window.location.href).toContain('github.com/login/oauth/authorize');
    expect(window.location.href).toContain('client_id=test-client-id');
  });

  it('should disconnect GitHub successfully', async () => {
    const mockDelete = jest.fn().mockReturnValue({
      eq: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
    });

    mockSupabase.from.mockReturnValue({
      delete: mockDelete,
    } as any);

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      await result.current.disconnectGitHub();
    });

    expect(result.current.isConnected).toBe(false);
    expect(result.current.user).toBeNull();
    expect(mockSupabase.from).toHaveBeenCalledWith('github_tokens');
  });

  it('should handle disconnect error', async () => {
    const mockDelete = jest.fn().mockReturnValue({
      eq: jest.fn().mockRejectedValue(new Error('Database error')),
    });

    mockSupabase.from.mockReturnValue({
      delete: mockDelete,
    } as any);

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      try {
        await result.current.disconnectGitHub();
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  it('should handle no session during disconnect', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    const { result } = renderHook(() => useGitHubAuth());

    await act(async () => {
      await result.current.disconnectGitHub();
    });

    // Should not throw error, just return early
    expect(mockSupabase.from).not.toHaveBeenCalled();
  });
});
