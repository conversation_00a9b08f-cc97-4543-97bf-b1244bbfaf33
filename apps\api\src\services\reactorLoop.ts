import { PlanAgent, CritiqueAgent, J<PERSON><PERSON>atch, LoopR<PERSON>ult } from '@metamorphic-reactor/agents';
import { supabaseService, ReactorSession } from './supabase.js';
import { createGitHubService, PullRequestResult } from './githubService.js';
import { secretsService } from './secretsService.js';
import { stripSecrets } from '../middleware/security.js';
import { queueService } from './queueService.js';
import { loggingService } from './loggingService.js';
import { billingService } from './billingService.js';

export interface LoopRequest {
  prompt: string;
  maxLoops?: number;
  context?: Record<string, any>;
  createPR?: boolean;
}

export interface LoopProgress {
  sessionId: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch: JSONPatch;
  isComplete: boolean;
}

export class ReactorLoopService {
  private planAgent: PlanAgent;
  private critiqueAgent: CritiqueAgent;
  private githubService: ReturnType<typeof createGitHubService>;

  constructor() {
    this.planAgent = new PlanAgent({
      model: 'gemini-2.5',
      temperature: 0.7,
      maxTokens: 2000
    });

    this.critiqueAgent = new CritiqueAgent({
      model: 'gemini-2.5',
      temperature: 0.3,
      maxTokens: 1500
    });

    this.githubService = createGitHubService();

    // Set up queue event handlers
    this.setupQueueHandlers();
  }

  /**
   * Set up queue event handlers
   */
  private setupQueueHandlers(): void {
    queueService.on('processing', async (queueItem) => {
      try {
        await loggingService.log({
          level: 'info',
          message: 'Starting queued reactor loop processing',
          service: 'reactor-loop',
          session_id: queueItem.sessionId,
          metadata: {
            queueItemId: queueItem.id,
            priority: queueItem.priority
          }
        });

        // Update session status to running
        await supabaseService.updateSession(queueItem.sessionId, {
          status: 'running'
        });

        // Execute the reactor loop
        const result = await this.runLoop(
          queueItem.data.request,
          undefined // No progress callback for queued items
        );

        // Record usage for billing
        try {
          await billingService.recordUsage(
            queueItem.sessionId, // Using sessionId as userId for now
            'transformation',
            1,
            queueItem.sessionId,
            {
              loops_completed: result.loops?.length || 0,
              total_patches: result.patches?.length || 0,
              quality_score: result.qualityScore
            }
          );
        } catch (usageError) {
          // Log but don't fail the reactor loop for billing issues
          await loggingService.log({
            level: 'warn',
            message: 'Failed to record usage for completed reactor loop',
            service: 'reactor-loop',
            session_id: queueItem.sessionId,
            metadata: {
              error: usageError instanceof Error ? usageError.message : 'Unknown error'
            }
          });
        }

        // Mark as completed in queue
        await queueService.markCompleted(queueItem.id, result);

      } catch (error) {
        await loggingService.log({
          level: 'error',
          message: 'Queued reactor loop processing failed',
          service: 'reactor-loop',
          session_id: queueItem.sessionId,
          metadata: {
            queueItemId: queueItem.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });

        // Mark as failed in queue
        await queueService.markFailed(queueItem.id, error instanceof Error ? error.message : 'Unknown error');
      }
    });

    queueService.on('failed', async (queueItem, error) => {
      await loggingService.log({
        level: 'error',
        message: 'Reactor loop permanently failed in queue',
        service: 'reactor-loop',
        session_id: queueItem.sessionId,
        metadata: {
          queueItemId: queueItem.id,
          error,
          retryCount: queueItem.retryCount
        }
      });

      // Update session status to failed
      await supabaseService.updateSession(queueItem.sessionId, {
        status: 'failed',
        error: error,
        completed_at: new Date().toISOString()
      });
    });
  }

  /**
   * Strip secrets from prompt before processing
   */
  private sanitizePrompt(prompt: string): string {
    return stripSecrets(prompt);
  }

  /**
   * Start a new reactor loop with queue management
   */
  async startQueuedLoop(request: LoopRequest, userId?: string): Promise<{ sessionId: string; queuePosition?: number }> {
    const maxLoops = Math.min(request.maxLoops || 5, 10); // Cap at 10 loops for safety

    // Check usage limits if user is provided
    if (userId) {
      const limitCheck = await billingService.checkUsageLimit(userId, 'transformation');

      if (!limitCheck.allowed) {
        await loggingService.log({
          level: 'warn',
          message: 'Reactor loop rejected due to usage limit',
          service: 'reactor-loop',
          user_id: userId,
          metadata: {
            current_usage: limitCheck.current,
            limit: limitCheck.limit,
            remaining: limitCheck.remaining
          }
        });

        throw new Error(`Usage limit exceeded. You have used ${limitCheck.current}/${limitCheck.limit} transformations this billing period.`);
      }
    }

    // Sanitize prompt to remove any secrets
    const sanitizedPrompt = this.sanitizePrompt(request.prompt);

    // Create session in database
    const session = await supabaseService.createSession({
      prompt: sanitizedPrompt,
      max_loops: maxLoops,
      status: 'queued'
    });

    // Check queue status
    const queueStatus = queueService.getStatus();

    // Add to queue with priority based on request urgency
    const priority = (request as any).priority || 0;
    const queueResult = await queueService.enqueue(
      session.id,
      { request: { ...request, prompt: sanitizedPrompt }, maxLoops },
      priority
    );

    if (!queueResult.success) {
      // Update session status to failed
      await supabaseService.updateSession(session.id, {
        status: 'failed',
        error: queueResult.error
      });

      await loggingService.log({
        level: 'warn',
        message: 'Reactor loop rejected due to queue limits',
        service: 'reactor-loop',
        session_id: session.id,
        metadata: {
          queueSize: queueStatus.queueLength,
          processing: queueStatus.processing,
          error: queueResult.error
        }
      });

      throw new Error(queueResult.error || 'Queue is full');
    }

    await loggingService.log({
      level: 'info',
      message: 'Reactor loop queued successfully',
      service: 'reactor-loop',
      session_id: session.id,
      metadata: {
        queuePosition: queueResult.queuePosition,
        queueSize: queueStatus.queueLength,
        processing: queueStatus.processing
      }
    });

    return {
      sessionId: session.id,
      queuePosition: queueResult.queuePosition
    };
  }

  async runLoop(
    request: LoopRequest,
    onProgress?: (progress: LoopProgress) => void
  ): Promise<LoopResult> {
    const maxLoops = request.maxLoops || 10;
    const targetScore = 0.95;
    
    // Sanitize prompt to remove any secrets
    const sanitizedPrompt = this.sanitizePrompt(request.prompt);

    // Create session in database
    const session = await supabaseService.createSession({
      prompt: sanitizedPrompt,
      max_loops: maxLoops,
      status: 'running'
    });

    const sessionId = session.id!;
    const logs: LoopResult['logs'] = [];
    let currentPatch: JSONPatch | null = null;
    let bestScore = 0;
    let bestPatch: JSONPatch | null = null;

    try {
      for (let iteration = 1; iteration <= maxLoops; iteration++) {
        console.log(`🔄 Starting iteration ${iteration}/${maxLoops}`);

        // Plan phase
        const planRequest = {
          prompt: sanitizedPrompt,
          context: request.context,
          previousAttempts: currentPatch ? [currentPatch] : undefined
        };

        currentPatch = await this.planAgent.generatePatch(planRequest);
        const planDescription = `Iteration ${iteration}: ${currentPatch.description}`;

        // Critique phase
        const critiqueRequest = {
          patch: currentPatch,
          originalPrompt: sanitizedPrompt,
          context: request.context
        };

        const critique = await this.critiqueAgent.scorePatch(critiqueRequest);
        const critiqueDescription = `Score: ${critique.score.toFixed(3)} - ${critique.feedback}`;

        // Log iteration
        const logEntry = {
          session_id: sessionId,
          iteration,
          plan: planDescription,
          critique: critiqueDescription,
          score: critique.score,
          patch: currentPatch
        };

        await supabaseService.logIteration(logEntry);

        // Track best result
        if (critique.score > bestScore) {
          bestScore = critique.score;
          bestPatch = currentPatch;
        }

        // Add to logs
        logs.push({
          iteration,
          plan: planDescription,
          critique: critiqueDescription,
          score: critique.score,
          patch: currentPatch
        });

        // Notify progress
        if (onProgress) {
          onProgress({
            sessionId,
            iteration,
            plan: planDescription,
            critique: critiqueDescription,
            score: critique.score,
            patch: currentPatch,
            isComplete: critique.score >= targetScore
          });
        }

        // Check if we've reached the target score
        if (critique.score >= targetScore) {
          console.log(`✅ Target score reached: ${critique.score.toFixed(3)}`);
          break;
        }

        console.log(`📊 Iteration ${iteration} score: ${critique.score.toFixed(3)}`);
      }

      // Create GitHub PR if requested and successful
      let prUrl: string | undefined;
      if (request.createPR && bestPatch && this.githubService && bestScore >= 0.8) {
        try {
          console.log('🔀 Creating GitHub PR for successful reactor run...');
          const prResult = await this.githubService.createReactorPR(
            sessionId,
            bestPatch,
            request.prompt,
            bestScore,
            logs.length
          );
          prUrl = prResult.url;
          console.log(`✅ GitHub PR created: ${prUrl}`);
        } catch (error) {
          console.error('❌ Failed to create GitHub PR:', error);
          // Don't fail the entire loop if PR creation fails
        }
      }

      // Update session with final results
      await supabaseService.updateSession(sessionId, {
        final_score: bestScore,
        final_patch: bestPatch,
        iterations_count: logs.length,
        status: 'completed',
        completed_at: new Date().toISOString(),
        github_pr_url: prUrl
      });

      return {
        finalPatch: bestPatch!,
        score: bestScore,
        iterations: logs.length,
        logs,
        prUrl
      };

    } catch (error) {
      console.error('❌ Loop execution failed:', error);
      
      // Update session with error status
      await supabaseService.updateSession(sessionId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });

      throw error;
    }
  }

  async getSessionProgress(sessionId: string): Promise<LoopProgress[]> {
    const logs = await supabaseService.getSessionLogs(sessionId);
    
    return logs.map(log => ({
      sessionId,
      iteration: log.iteration,
      plan: log.plan,
      critique: log.critique,
      score: log.score,
      patch: log.patch,
      isComplete: log.score >= 0.95
    }));
  }
}

export const reactorLoopService = new ReactorLoopService();
