import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface LoopRequest {
  prompt: string;
  maxIterations?: number;
  scoreThreshold?: number;
  context?: Record<string, any>;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// AI API keys
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY');

class StreamingAILoop {
  private encoder = new TextEncoder();
  private startTime: number;
  private totalCost: number = 0;

  constructor() {
    this.startTime = Date.now();
  }

  private sendEvent(controller: ReadableStreamDefaultController, type: string, data: any) {
    const event = `data: ${JSON.stringify({ type, data, timestamp: new Date().toISOString() })}\n\n`;
    controller.enqueue(this.encoder.encode(event));
  }

  private checkGuards() {
    const elapsed = Date.now() - this.startTime;
    if (elapsed > 180000) { // 3 minutes
      throw new Error('Execution time limit exceeded');
    }
    if (this.totalCost > 3.0) {
      throw new Error('Cost limit exceeded');
    }
  }

  private stripSecrets(text: string): string {
    return text
      .replace(/sk-[a-zA-Z0-9]{48}/g, '[REDACTED_API_KEY]')
      .replace(/ghp_[a-zA-Z0-9]{36}/g, '[REDACTED_GITHUB_TOKEN]')
      .replace(/password[:\s]*[^\s\n]+/gi, 'password: [REDACTED]')
      .replace(/token[:\s]*[^\s\n]+/gi, 'token: [REDACTED]');
  }

  async callGPT4(prompt: string): Promise<{ content: string; tokens: number }> {
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: this.stripSecrets(prompt) }],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    const tokens = data.usage?.total_tokens || 1000;
    this.totalCost += tokens * 0.00003;

    return {
      content: data.choices[0]?.message?.content || '',
      tokens,
    };
  }

  async callClaude(prompt: string): Promise<{ content: string; tokens: number }> {
    if (!anthropicApiKey) {
      throw new Error('Anthropic API key not configured');
    }

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': anthropicApiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 1500,
        temperature: 0.3,
        messages: [{ role: 'user', content: this.stripSecrets(prompt) }],
      }),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`);
    }

    const data = await response.json();
    const tokens = data.usage?.input_tokens + data.usage?.output_tokens || 800;
    this.totalCost += tokens * 0.000008;

    return {
      content: data.content[0]?.text || '',
      tokens,
    };
  }

  async *runLoop(request: LoopRequest, sessionId: string) {
    const { prompt, maxIterations = 10, scoreThreshold = 0.95 } = request;
    const previousAttempts: any[] = [];
    let iteration = 0;
    let finalScore = 0;
    let currentPatch: any = null;

    yield { type: 'start', data: { sessionId, prompt, maxIterations, scoreThreshold } };

    while (iteration < maxIterations) {
      iteration++;
      this.checkGuards();

      try {
        yield { type: 'iteration_start', data: { iteration } };

        // Generate patch
        yield { type: 'plan_start', data: { iteration } };
        
        const planningPrompt = `
You are a Plan Agent. Generate a JSON patch for: ${prompt}

${previousAttempts.length > 0 ? `Previous attempts: ${previousAttempts.map(p => p.description).join(', ')}` : ''}

Return valid JSON:
{
  "operations": [{"op": "add|remove|replace", "path": "/path", "value": "value"}],
  "description": "What this patch does",
  "confidence": 0.85
}`;

        let patch;
        try {
          const result = await this.callGPT4(planningPrompt);
          patch = JSON.parse(result.content);
        } catch (error) {
          patch = {
            operations: [{ op: 'add', path: '/fallback', value: { prompt, iteration } }],
            description: `Fallback patch for iteration ${iteration}`,
            confidence: 0.6,
          };
        }

        currentPatch = patch;
        yield { type: 'plan_complete', data: { iteration, patch } };

        // Critique patch
        yield { type: 'critique_start', data: { iteration } };

        const critiquePrompt = `
Evaluate this JSON patch for prompt "${prompt}":
${JSON.stringify(patch, null, 2)}

Return JSON:
{
  "score": 0.92,
  "feedback": "Detailed analysis",
  "suggestions": ["suggestion1", "suggestion2"],
  "isAcceptable": true
}`;

        let critique;
        try {
          const result = await this.callClaude(critiquePrompt);
          critique = JSON.parse(result.content);
        } catch (error) {
          const mockScore = 0.7 + Math.random() * 0.2;
          critique = {
            score: mockScore,
            feedback: `Fallback critique for iteration ${iteration}`,
            suggestions: ['Add error handling', 'Improve validation'],
            isAcceptable: mockScore >= scoreThreshold,
          };
        }

        finalScore = critique.score;
        yield { type: 'critique_complete', data: { iteration, critique } };

        // Log to database
        const logEntry = {
          session_id: sessionId,
          iteration,
          plan: patch.description,
          critique: critique.feedback,
          score: critique.score,
          patch,
        };

        await supabase.from('agent_logs').insert(logEntry);
        yield { type: 'log_saved', data: { iteration, score: critique.score } };

        // Check completion
        if (critique.isAcceptable || critique.score >= scoreThreshold) {
          yield { type: 'complete', data: { 
            finalPatch: patch, 
            finalScore: critique.score, 
            iterations: iteration,
            reason: 'threshold_met'
          } };
          break;
        }

        previousAttempts.push(patch);
        yield { type: 'iteration_complete', data: { iteration, score: critique.score } };

      } catch (error) {
        yield { type: 'error', data: { iteration, error: error.message } };
        break;
      }
    }

    if (iteration >= maxIterations) {
      yield { type: 'complete', data: { 
        finalPatch: currentPatch, 
        finalScore, 
        iterations: iteration,
        reason: 'max_iterations'
      } };
    }

    // Update session
    await supabase
      .from('reactor_sessions')
      .update({
        final_score: finalScore,
        final_patch: currentPatch,
        iterations_count: iteration,
        status: 'completed',
        completed_at: new Date().toISOString(),
      })
      .eq('id', sessionId);
  }
}

Deno.serve(async (req: Request) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const request: LoopRequest = await req.json();

    if (!request.prompt?.trim()) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create session
    const { data: session, error: sessionError } = await supabase
      .from('reactor_sessions')
      .insert({
        prompt: request.prompt,
        max_loops: request.maxIterations || 10,
        status: 'running',
      })
      .select()
      .single();

    if (sessionError) {
      throw new Error(`Failed to create session: ${sessionError.message}`);
    }

    const aiLoop = new StreamingAILoop();

    const stream = new ReadableStream({
      async start(controller) {
        try {
          for await (const event of aiLoop.runLoop(request, session.id)) {
            const eventData = `data: ${JSON.stringify(event)}\n\n`;
            controller.enqueue(new TextEncoder().encode(eventData));
          }
          controller.close();
        } catch (error) {
          const errorEvent = `data: ${JSON.stringify({ 
            type: 'error', 
            data: { error: error.message } 
          })}\n\n`;
          controller.enqueue(new TextEncoder().encode(errorEvent));
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Streaming AI Loop error:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
