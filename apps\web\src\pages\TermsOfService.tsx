import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Separator } from '../components/ui/separator';
import { Badge } from '../components/ui/badge';
import { Alert, AlertDescription } from '../components/ui/alert';
import { FileText, Users, CreditCard, Shield, AlertTriangle, Scale } from 'lucide-react';

export const TermsOfService: React.FC = () => {
  const lastUpdated = "January 15, 2024";
  const effectiveDate = "January 15, 2024";
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <FileText className="w-8 h-8 text-blue-600" />
            <h1 className="text-4xl font-bold">Terms of Service</h1>
          </div>
          <p className="text-lg text-muted-foreground">
            Legal terms and conditions for using Metamorphic Reactor
          </p>
          <div className="flex justify-center space-x-4">
            <Badge variant="secondary">
              Last updated: {lastUpdated}
            </Badge>
            <Badge variant="outline">
              Effective: {effectiveDate}
            </Badge>
          </div>
        </div>

        {/* Important Notice */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Important:</strong> By using Metamorphic Reactor, you agree to these terms. 
            If you don't agree, please don't use our service. These terms include important 
            information about your rights and obligations.
          </AlertDescription>
        </Alert>

        {/* Acceptance of Terms */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Scale className="w-5 h-5" />
              <span>Acceptance of Terms</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              These Terms of Service ("Terms") govern your use of Metamorphic Reactor ("Service") 
              operated by Metamorphic Reactor Inc. ("us", "we", or "our").
            </p>
            <p>
              By accessing or using our Service, you agree to be bound by these Terms. 
              If you disagree with any part of these terms, you may not access the Service.
            </p>
          </CardContent>
        </Card>

        {/* Service Description */}
        <Card>
          <CardHeader>
            <CardTitle>Service Description</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Metamorphic Reactor is an AI-powered code transformation service that helps developers 
              modify, improve, and refactor their code using advanced language models.
            </p>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">What We Provide</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>AI-powered code analysis and transformation</li>
                  <li>Integration with GitHub repositories</li>
                  <li>Automated pull request creation</li>
                  <li>Code quality scoring and feedback</li>
                  <li>Usage analytics and monitoring</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">What We Don't Provide</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Guaranteed code correctness</li>
                  <li>Software development consulting</li>
                  <li>Code hosting or version control</li>
                  <li>Technical support for your code</li>
                  <li>Liability for code-related issues</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>User Accounts</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Account Creation</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>You must provide accurate and complete information</li>
                <li>You are responsible for maintaining account security</li>
                <li>You must be at least 18 years old or have parental consent</li>
                <li>One person or entity may not maintain multiple accounts</li>
              </ul>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2">Account Responsibilities</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Keep your login credentials secure and confidential</li>
                <li>Notify us immediately of any unauthorized access</li>
                <li>You are liable for all activities under your account</li>
                <li>Comply with all applicable laws and regulations</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Acceptable Use */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Acceptable Use Policy</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2 text-green-600">✅ Permitted Uses</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Transform and improve your own code</li>
                <li>Generate code patches and modifications</li>
                <li>Integrate with your development workflow</li>
                <li>Use for commercial and non-commercial projects</li>
                <li>Share generated code (subject to your own licensing)</li>
              </ul>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2 text-red-600">❌ Prohibited Uses</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Reverse engineer or attempt to extract our AI models</li>
                <li>Use the service to generate malicious or harmful code</li>
                <li>Violate intellectual property rights of others</li>
                <li>Attempt to overwhelm or abuse our infrastructure</li>
                <li>Use for illegal activities or regulatory violations</li>
                <li>Share or resell access to the service</li>
                <li>Submit code containing personal or sensitive data</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Billing and Payments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="w-5 h-5" />
              <span>Billing and Payments</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Subscription Plans</h4>
              <p className="text-sm text-muted-foreground mb-3">
                We offer various subscription plans with different usage limits and features.
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Free tier with limited monthly transformations</li>
                <li>Paid plans with higher limits and premium features</li>
                <li>Enterprise plans with custom limits and support</li>
                <li>Usage-based billing for overages</li>
              </ul>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2">Payment Terms</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Payments are processed monthly in advance</li>
                <li>All fees are non-refundable unless required by law</li>
                <li>We may change pricing with 30 days notice</li>
                <li>Failed payments may result in service suspension</li>
                <li>You're responsible for all applicable taxes</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Intellectual Property */}
        <Card>
          <CardHeader>
            <CardTitle>Intellectual Property Rights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Your Code</h4>
              <p className="text-sm">
                You retain all rights to your original code. By using our service, you grant us 
                a limited license to process your code for the purpose of providing transformations.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Generated Code</h4>
              <p className="text-sm">
                You own the code generated by our service. However, similar code may be generated 
                for other users. We don't claim ownership of generated code.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Our Service</h4>
              <p className="text-sm">
                The Metamorphic Reactor service, including our AI models, algorithms, and interface, 
                are our intellectual property and protected by copyright and other laws.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Privacy and Data */}
        <Card>
          <CardHeader>
            <CardTitle>Privacy and Data Handling</CardTitle>
            <CardDescription>
              How we handle your code and personal information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Code Processing</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Your code is processed by AI models to generate transformations</li>
                <li>Code may be temporarily cached for performance optimization</li>
                <li>We don't use your code to train our models without consent</li>
                <li>Code is automatically deleted after processing (90 days max)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Data Security</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>All data is encrypted in transit and at rest</li>
                <li>Access is limited to authorized personnel only</li>
                <li>We follow industry-standard security practices</li>
                <li>Regular security audits and monitoring</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Disclaimers and Limitations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5" />
              <span>Disclaimers and Limitations</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> AI-generated code may contain errors, security vulnerabilities, 
                or other issues. Always review and test generated code before using in production.
              </AlertDescription>
            </Alert>
            
            <div>
              <h4 className="font-semibold mb-2">Service Availability</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>We strive for high availability but don't guarantee 100% uptime</li>
                <li>Scheduled maintenance may temporarily interrupt service</li>
                <li>We may suspend service for security or legal reasons</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Limitation of Liability</h4>
              <p className="text-sm">
                To the maximum extent permitted by law, we shall not be liable for any indirect, 
                incidental, special, consequential, or punitive damages, including lost profits, 
                data loss, or business interruption.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Termination */}
        <Card>
          <CardHeader>
            <CardTitle>Termination</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">By You</h4>
              <p className="text-sm">
                You may terminate your account at any time by contacting us or using the 
                account deletion feature. Termination is effective immediately.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">By Us</h4>
              <p className="text-sm">
                We may terminate or suspend your account for violations of these terms, 
                non-payment, or other legitimate reasons with appropriate notice.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Effect of Termination</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Your access to the service will be immediately revoked</li>
                <li>Your data will be deleted according to our retention policy</li>
                <li>Outstanding fees remain due and payable</li>
                <li>Provisions that should survive termination will remain in effect</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Governing Law */}
        <Card>
          <CardHeader>
            <CardTitle>Governing Law and Disputes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              These Terms are governed by the laws of [Jurisdiction] without regard to conflict 
              of law principles. Any disputes will be resolved through binding arbitration or 
              in the courts of [Jurisdiction].
            </p>
            
            <div>
              <h4 className="font-semibold mb-2">Dispute Resolution</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>First, contact us to try to resolve the issue informally</li>
                <li>If unsuccessful, disputes may be submitted to binding arbitration</li>
                <li>Class action lawsuits are waived to the extent permitted by law</li>
              </ol>
            </div>
          </CardContent>
        </Card>

        {/* Changes to Terms */}
        <Card>
          <CardHeader>
            <CardTitle>Changes to These Terms</CardTitle>
          </CardHeader>
          <CardContent>
            <p>
              We may modify these Terms at any time. Material changes will be communicated 
              via email or prominent notice in the service. Continued use after changes 
              constitutes acceptance of the new terms.
            </p>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Questions about these Terms? Contact us:
            </p>
            
            <div className="space-y-2">
              <div>
                <span className="font-medium">Email:</span>
                <span className="ml-2"><EMAIL></span>
              </div>
              <div>
                <span className="font-medium">Address:</span>
                <span className="ml-2">[Company Address]</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
