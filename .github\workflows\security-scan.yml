name: Security Scan

on:
  push:
    branches: [ main, feat/prod-hardening ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run weekly security scan on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: true
        default: 'baseline'
        type: choice
        options:
          - baseline
          - full
          - api-only

env:
  NODE_VERSION: '18'

jobs:
  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: |
          echo "Running npm audit..."
          npm audit --audit-level=moderate --production
          
      - name: Check for high/critical vulnerabilities
        run: |
          echo "Checking for high/critical vulnerabilities..."
          AUDIT_RESULT=$(npm audit --audit-level=high --production --json || true)
          
          if echo "$AUDIT_RESULT" | jq -e '.metadata.vulnerabilities.high > 0 or .metadata.vulnerabilities.critical > 0' > /dev/null; then
            echo "❌ High or critical vulnerabilities found!"
            echo "$AUDIT_RESULT" | jq '.advisories'
            exit 1
          else
            echo "✅ No high or critical vulnerabilities found"
          fi
          
      - name: Generate dependency report
        run: |
          echo "Generating dependency security report..."
          npm audit --json > dependency-audit.json || true
          
      - name: Upload dependency audit results
        uses: actions/upload-artifact@v4
        with:
          name: dependency-audit-report
          path: dependency-audit.json
          retention-days: 30

  owasp-zap-scan:
    name: OWASP ZAP Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: dependency-check
    
    services:
      api:
        image: node:18
        ports:
          - 3001:3001
        env:
          NODE_ENV: test
          PORT: 3001
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          CORS_ORIGIN: http://localhost:5173
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build API
        run: npm run build --workspace=apps/api
        
      - name: Start API server
        run: |
          cd apps/api
          npm start &
          sleep 10
          
          # Wait for API to be ready
          for i in {1..30}; do
            if curl -f http://localhost:3001/health; then
              echo "API is ready"
              break
            fi
            echo "Waiting for API... ($i/30)"
            sleep 2
          done
        
      - name: Run OWASP ZAP Baseline Scan
        if: github.event.inputs.scan_type == 'baseline' || github.event.inputs.scan_type == ''
        uses: zaproxy/action-baseline@v0.12.0
        with:
          target: 'http://localhost:3001'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a -j -m 5 -T 15'
          
      - name: Run OWASP ZAP Full Scan
        if: github.event.inputs.scan_type == 'full'
        uses: zaproxy/action-full-scan@v0.10.0
        with:
          target: 'http://localhost:3001'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a -j -m 5 -T 30'
          
      - name: Run OWASP ZAP API Scan
        if: github.event.inputs.scan_type == 'api-only'
        uses: zaproxy/action-api-scan@v0.7.0
        with:
          target: 'http://localhost:3001'
          format: openapi
          api_spec: 'docs/api-spec.json'
          cmd_options: '-a -j -m 5 -T 20'
          
      - name: Upload ZAP scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: zap-scan-results
          path: |
            report_html.html
            report_json.json
            report_md.md
          retention-days: 30
          
      - name: Check ZAP scan results
        run: |
          if [ -f report_json.json ]; then
            echo "Analyzing ZAP scan results..."
            
            # Count high and medium severity issues
            HIGH_COUNT=$(jq '[.site[].alerts[] | select(.riskdesc | contains("High"))] | length' report_json.json)
            MEDIUM_COUNT=$(jq '[.site[].alerts[] | select(.riskdesc | contains("Medium"))] | length' report_json.json)
            
            echo "High severity issues: $HIGH_COUNT"
            echo "Medium severity issues: $MEDIUM_COUNT"
            
            # Fail if high severity issues found
            if [ "$HIGH_COUNT" -gt 0 ]; then
              echo "❌ High severity security issues found!"
              jq '.site[].alerts[] | select(.riskdesc | contains("High")) | {name, riskdesc, desc}' report_json.json
              exit 1
            fi
            
            # Warn about medium severity issues
            if [ "$MEDIUM_COUNT" -gt 0 ]; then
              echo "⚠️ Medium severity security issues found:"
              jq '.site[].alerts[] | select(.riskdesc | contains("Medium")) | {name, riskdesc, desc}' report_json.json
            fi
            
            echo "✅ Security scan completed successfully"
          else
            echo "No ZAP report found"
          fi

  security-headers-check:
    name: Security Headers Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: dependency-check
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build and start API
        run: |
          npm run build --workspace=apps/api
          cd apps/api
          npm start &
          sleep 10
        env:
          NODE_ENV: test
          PORT: 3001
          
      - name: Check security headers
        run: |
          echo "Checking security headers..."
          
          # Test health endpoint
          RESPONSE=$(curl -I http://localhost:3001/health)
          echo "Response headers:"
          echo "$RESPONSE"
          
          # Check for required security headers
          MISSING_HEADERS=()
          
          if ! echo "$RESPONSE" | grep -i "x-content-type-options"; then
            MISSING_HEADERS+=("X-Content-Type-Options")
          fi
          
          if ! echo "$RESPONSE" | grep -i "x-frame-options"; then
            MISSING_HEADERS+=("X-Frame-Options")
          fi
          
          if ! echo "$RESPONSE" | grep -i "strict-transport-security"; then
            MISSING_HEADERS+=("Strict-Transport-Security")
          fi
          
          if ! echo "$RESPONSE" | grep -i "content-security-policy"; then
            MISSING_HEADERS+=("Content-Security-Policy")
          fi
          
          if ! echo "$RESPONSE" | grep -i "referrer-policy"; then
            MISSING_HEADERS+=("Referrer-Policy")
          fi
          
          # Check if X-Powered-By is removed
          if echo "$RESPONSE" | grep -i "x-powered-by"; then
            MISSING_HEADERS+=("X-Powered-By should be removed")
          fi
          
          if [ ${#MISSING_HEADERS[@]} -eq 0 ]; then
            echo "✅ All required security headers are present"
          else
            echo "❌ Missing security headers:"
            printf '%s\n' "${MISSING_HEADERS[@]}"
            exit 1
          fi

  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-check, owasp-zap-scan, security-headers-check]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        
      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "" >> security-summary.md
          echo "**Scan Date:** $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> security-summary.md
          echo "**Repository:** ${{ github.repository }}" >> security-summary.md
          echo "**Branch:** ${{ github.ref_name }}" >> security-summary.md
          echo "" >> security-summary.md
          
          # Job statuses
          echo "## Job Results" >> security-summary.md
          echo "- **Dependency Check:** ${{ needs.dependency-check.result }}" >> security-summary.md
          echo "- **OWASP ZAP Scan:** ${{ needs.owasp-zap-scan.result }}" >> security-summary.md
          echo "- **Security Headers:** ${{ needs.security-headers-check.result }}" >> security-summary.md
          echo "" >> security-summary.md
          
          # Overall status
          if [[ "${{ needs.dependency-check.result }}" == "success" && 
                "${{ needs.owasp-zap-scan.result }}" == "success" && 
                "${{ needs.security-headers-check.result }}" == "success" ]]; then
            echo "## Overall Status: ✅ PASS" >> security-summary.md
          else
            echo "## Overall Status: ❌ FAIL" >> security-summary.md
          fi
          
          echo "" >> security-summary.md
          echo "## Recommendations" >> security-summary.md
          echo "- Review any failed checks above" >> security-summary.md
          echo "- Update dependencies with known vulnerabilities" >> security-summary.md
          echo "- Address any medium/high severity security issues" >> security-summary.md
          echo "- Ensure all security headers are properly configured" >> security-summary.md
          
      - name: Upload security summary
        uses: actions/upload-artifact@v4
        with:
          name: security-summary
          path: security-summary.md
          retention-days: 90
          
      - name: Send Slack notification
        if: failure()
        run: |
          if [ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]; then
            curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "🚨 Security Scan Failed",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Security Scan Alert*\n\n• *Repository:* ${{ github.repository }}\n• *Branch:* ${{ github.ref_name }}\n• *Status:* ❌ Failed\n• *Workflow:* [View Results](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n• *Action Required:* Review security issues and fix vulnerabilities"
                    }
                  }
                ]
              }'
          fi
