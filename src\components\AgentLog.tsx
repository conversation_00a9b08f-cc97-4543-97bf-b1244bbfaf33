
import { useEffect, useRef } from 'react';
import { Badge } from "@/components/ui/badge";
import { Bot, Eye, Cog } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface LogEntry {
  id: number;
  agent: 'planner' | 'critic' | 'system';
  message: string;
  timestamp: Date;
}

interface AgentLogProps {
  logs: LogEntry[];
}

export const AgentLog = ({ logs }: AgentLogProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  const getAgentIcon = (agent: string) => {
    switch (agent) {
      case 'planner': return <Bot className="w-4 h-4" />;
      case 'critic': return <Eye className="w-4 h-4" />;
      case 'system': return <Cog className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const getAgentColor = (agent: string) => {
    switch (agent) {
      case 'planner': return "bg-agent-planner/20 text-agent-planner-foreground border-agent-planner/30";
      case 'critic': return "bg-agent-critic/20 text-agent-critic-foreground border-agent-critic/30";
      case 'system': return "bg-agent-system/20 text-agent-system-foreground border-agent-system/30";
      default: return "bg-agent-system/20 text-agent-system-foreground border-agent-system/30";
    }
  };

  return (
    <div 
      ref={scrollRef}
      className="h-full bg-slate-900 overflow-auto p-4 space-y-3"
    >
      {logs.map((log) => (
        <div key={log.id} className="flex items-start space-x-3 group">
          <div className="flex-shrink-0 mt-1">
            <Badge className={`${getAgentColor(log.agent)} px-2 py-1`}>
              <div className="flex items-center space-x-1">
                {getAgentIcon(log.agent)}
                <span className="text-xs font-medium capitalize">{log.agent}</span>
              </div>
            </Badge>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm text-slate-300 leading-relaxed">{log.message}</p>
            <p className="text-xs text-slate-500 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {formatDistanceToNow(log.timestamp, { addSuffix: true })}
            </p>
          </div>
        </div>
      ))}
      
      {logs.length === 0 && (
        <div className="text-center py-8 text-slate-500">
          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-slate-800 flex items-center justify-center">
            <Bot className="w-6 h-6" />
          </div>
          <p className="text-sm">No logs yet</p>
          <p className="text-xs">Agent logs will appear here</p>
        </div>
      )}
    </div>
  );
};
