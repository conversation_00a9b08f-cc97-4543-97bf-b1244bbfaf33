
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Code, Zap, Cpu, Database } from "lucide-react";

interface CodeExample {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'algorithms' | 'async' | 'patterns';
  code: string;
  icon: React.ReactNode;
}

interface CodeExamplesProps {
  onSelectExample: (code: string) => void;
}

const codeExamples: CodeExample[] = [
  {
    id: 'fibonacci',
    title: '<PERSON><PERSON><PERSON><PERSON> (Inefficient)',
    description: 'Classic recursive implementation without memoization',
    category: 'performance',
    icon: <Zap className="w-4 h-4" />,
    code: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fi<PERSON><PERSON><PERSON>(10));`
  },
  {
    id: 'bubble-sort',
    title: 'Bubble Sort',
    description: 'Inefficient sorting algorithm that can be optimized',
    category: 'algorithms',
    icon: <Cpu className="w-4 h-4" />,
    code: `function bubbleSort(arr) {
  var n = arr.length;
  for (var i = 0; i < n; i++) {
    for (var j = 0; j < n - i - 1; j++) {
      if (arr[j] > arr[j + 1]) {
        var temp = arr[j];
        arr[j] = arr[j + 1];
        arr[j + 1] = temp;
      }
    }
  }
  return arr;
}

console.log(bubbleSort([64, 34, 25, 12, 22, 11, 90]));`
  },
  {
    id: 'callback-hell',
    title: 'Callback Hell',
    description: 'Nested callbacks that can be converted to async/await',
    category: 'async',
    icon: <Database className="w-4 h-4" />,
    code: `function fetchUserData(userId) {
  getUserById(userId, function(user) {
    getProfileById(user.profileId, function(profile) {
      getPreferencesById(profile.preferencesId, function(preferences) {
        console.log('User data:', { user, profile, preferences });
      });
    });
  });
}

fetchUserData(123);`
  },
  {
    id: 'var-declarations',
    title: 'Old JavaScript Patterns',
    description: 'Legacy code with var declarations and function expressions',
    category: 'patterns',
    icon: <Code className="w-4 h-4" />,
    code: `var userName = 'John';
var userAge = 30;

function calculateAge(birthYear) {
  var currentYear = new Date().getFullYear();
  return currentYear - birthYear;
}

var processUser = function(user) {
  var processed = {
    name: user.name,
    age: calculateAge(user.birthYear),
    isAdult: user.age >= 18
  };
  return processed;
};`
  }
];

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'performance': return 'bg-destructive/20 text-destructive-foreground border-destructive/30';
    case 'algorithms': return 'bg-info/20 text-info-foreground border-info/30';
    case 'async': return 'bg-success/20 text-success-foreground border-success/30';
    case 'patterns': return 'bg-agent-planner/20 text-agent-planner-foreground border-agent-planner/30';
    default: return 'bg-muted/20 text-muted-foreground border-muted/30';
  }
};

export const CodeExamples = ({ onSelectExample }: CodeExamplesProps) => {
  return (
    <div className="p-4 space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-foreground mb-2">Code Examples</h3>
        <p className="text-sm text-muted-foreground">Click an example to load it into the editor</p>
      </div>
      
      <div className="grid gap-3">
        {codeExamples.map((example) => (
          <Card key={example.id} className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors cursor-pointer">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {example.icon}
                  <CardTitle className="text-sm text-white">{example.title}</CardTitle>
                </div>
                <Badge className={getCategoryColor(example.category)}>
                  {example.category}
                </Badge>
              </div>
              <CardDescription className="text-xs text-slate-400">
                {example.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSelectExample(example.code)}
                className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Load Example
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
