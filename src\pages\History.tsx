
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Search, Clock, TrendingUp, FileCode, Eye } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface HistoryEntry {
  id: string;
  title: string;
  timestamp: Date;
  score: number;
  iterations: number;
  language: string;
  status: 'completed' | 'running' | 'failed';
}

const History = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  // Mock history data
  const [historyEntries] = useState<HistoryEntry[]>([
    {
      id: "1",
      title: "Fibonacci Optimization",
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      score: 0.96,
      iterations: 3,
      language: "javascript",
      status: "completed"
    },
    {
      id: "2",
      title: "React Component Refactor",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      score: 0.89,
      iterations: 5,
      language: "typescript",
      status: "completed"
    },
    {
      id: "3",
      title: "API Performance Enhancement",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      score: 0.94,
      iterations: 4,
      language: "python",
      status: "completed"
    },
    {
      id: "4",
      title: "Database Query Optimization",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
      score: 0.82,
      iterations: 7,
      language: "sql",
      status: "failed"
    },
    {
      id: "5",
      title: "Algorithm Improvement",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
      score: 0.91,
      iterations: 2,
      language: "javascript",
      status: "completed"
    }
  ]);

  const filteredEntries = historyEntries.filter(entry =>
    entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    entry.language.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return "bg-green-600/20 text-green-300 border-green-500/30";
      case 'running': return "bg-blue-600/20 text-blue-300 border-blue-500/30";
      case 'failed': return "bg-red-600/20 text-red-300 border-red-500/30";
      default: return "bg-slate-600/20 text-slate-300 border-slate-500/30";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.9) return "text-green-400";
    if (score >= 0.8) return "text-yellow-400";
    return "text-red-400";
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Dashboard
            </Button>
            <h1 className="text-2xl font-bold text-white">History</h1>
            <Badge variant="secondary" className="bg-green-600/20 text-green-300 border-green-500/30">
              {filteredEntries.length} Sessions
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 max-w-6xl">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-3 w-4 h-4 text-slate-400" />
            <Input
              placeholder="Search sessions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-slate-800 border-slate-700 text-white placeholder:text-slate-500"
            />
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-sm font-medium">Total Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-3xl font-bold text-white">{historyEntries.length}</span>
                <FileCode className="w-5 h-5 text-slate-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-sm font-medium">Average Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-3xl font-bold text-green-400">
                  {(historyEntries.reduce((acc, entry) => acc + entry.score, 0) / historyEntries.length).toFixed(2)}
                </span>
                <TrendingUp className="w-5 h-5 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-sm font-medium">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-3xl font-bold text-blue-400">
                  {Math.round((historyEntries.filter(e => e.status === 'completed').length / historyEntries.length) * 100)}%
                </span>
                <Clock className="w-5 h-5 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* History List */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-white mb-4">Recent Sessions</h2>
          
          {filteredEntries.length === 0 ? (
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="py-12 text-center">
                <FileCode className="w-12 h-12 mx-auto mb-4 text-slate-500" />
                <p className="text-slate-400 text-lg">No sessions found</p>
                <p className="text-slate-500 text-sm">Try adjusting your search query</p>
              </CardContent>
            </Card>
          ) : (
            filteredEntries.map((entry) => (
              <Card key={entry.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-white">{entry.title}</h3>
                        <Badge className={getStatusColor(entry.status)}>
                          {entry.status}
                        </Badge>
                        <Badge variant="outline" className="border-slate-600 text-slate-300">
                          {entry.language}
                        </Badge>
                      </div>
                      <p className="text-slate-400 text-sm">
                        {formatDistanceToNow(entry.timestamp, { addSuffix: true })}
                      </p>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-xs text-slate-400 mb-1">Score</p>
                        <p className={`text-lg font-bold ${getScoreColor(entry.score)}`}>
                          {entry.score.toFixed(2)}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-xs text-slate-400 mb-1">Iterations</p>
                        <p className="text-lg font-bold text-white">{entry.iterations}</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default History;
