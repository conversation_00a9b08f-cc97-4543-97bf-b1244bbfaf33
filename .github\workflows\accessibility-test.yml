name: Accessibility Testing

on:
  push:
    branches: [ main, feat/prod-hardening ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_url:
        description: 'URL to test (default: deployed preview)'
        required: false
        type: string

env:
  NODE_VERSION: '18'

jobs:
  accessibility-audit:
    name: Accessibility Audit
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Puppeteer dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libnss3-dev \
            libatk-bridge2.0-dev \
            libdrm-dev \
            libxcomposite-dev \
            libxdamage-dev \
            libxrandr-dev \
            libgbm-dev \
            libxss-dev \
            libasound2-dev
            
      - name: Install accessibility testing packages
        run: |
          npm install @axe-core/puppeteer axe-core puppeteer
          
      - name: Build frontend
        run: npm run build --workspace=apps/web
        
      - name: Start API server
        run: |
          cd apps/api
          npm run build
          npm start &
          sleep 10
        env:
          NODE_ENV: test
          PORT: 3001
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          
      - name: Start frontend server
        run: |
          cd apps/web
          npm run preview -- --port 5173 --host &
          sleep 10
        env:
          VITE_API_URL: http://localhost:3001
          
      - name: Wait for servers to be ready
        run: |
          # Wait for API
          for i in {1..30}; do
            if curl -f http://localhost:3001/health; then
              echo "API is ready"
              break
            fi
            echo "Waiting for API... ($i/30)"
            sleep 2
          done
          
          # Wait for frontend
          for i in {1..30}; do
            if curl -f http://localhost:5173; then
              echo "Frontend is ready"
              break
            fi
            echo "Waiting for frontend... ($i/30)"
            sleep 2
          done
          
      - name: Run accessibility audit
        run: |
          chmod +x scripts/accessibility-audit.js
          node scripts/accessibility-audit.js
        env:
          BASE_URL: ${{ github.event.inputs.test_url || 'http://localhost:5173' }}
          
      - name: Upload accessibility reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-reports
          path: accessibility-reports/
          retention-days: 30
          
      - name: Check accessibility score
        run: |
          if [ -f accessibility-reports/accessibility-summary.json ]; then
            SCORE=$(jq -r '.overallScore' accessibility-reports/accessibility-summary.json)
            PASSED=$(jq -r '.passed' accessibility-reports/accessibility-summary.json)
            
            echo "Accessibility Score: $SCORE/100"
            echo "Passed: $PASSED"
            
            if [ "$PASSED" != "true" ]; then
              echo "❌ Accessibility audit failed!"
              echo "Score: $SCORE (required: 95)"
              
              # Show violations summary
              echo "Violations found:"
              jq -r '.results[] | select(.violations > 0) | "\(.browser) - \(.page): \(.violations) violations"' accessibility-reports/accessibility-summary.json
              
              exit 1
            else
              echo "✅ Accessibility audit passed!"
            fi
          else
            echo "❌ Accessibility report not found"
            exit 1
          fi
          
      - name: Comment PR with accessibility results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = 'accessibility-reports/accessibility-summary.json';
            
            if (!fs.existsSync(path)) {
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: '❌ **Accessibility Audit Failed**\n\nCould not generate accessibility report. Check the workflow logs for details.'
              });
              return;
            }
            
            const summary = JSON.parse(fs.readFileSync(path, 'utf8'));
            const passed = summary.passed;
            const score = summary.overallScore.toFixed(1);
            
            const emoji = passed ? '✅' : '❌';
            const status = passed ? 'PASSED' : 'FAILED';
            
            let comment = `${emoji} **Accessibility Audit ${status}**\n\n`;
            comment += `**Overall Score:** ${score}/100\n`;
            comment += `**Threshold:** 95/100\n\n`;
            
            if (summary.results.length > 0) {
              comment += '**Results by Page:**\n';
              summary.results.forEach(result => {
                const pageEmoji = result.score >= 95 ? '✅' : '❌';
                comment += `- ${pageEmoji} ${result.browser} - ${result.page}: ${result.score?.toFixed(1) || 'Error'}/100`;
                if (result.violations > 0) {
                  comment += ` (${result.violations} violations)`;
                }
                comment += '\n';
              });
            }
            
            if (!passed && summary.recommendations.length > 0) {
              comment += '\n**Recommendations:**\n';
              summary.recommendations.forEach(rec => {
                comment += `- ${rec}\n`;
              });
            }
            
            comment += '\n📊 [View detailed report in workflow artifacts](';
            comment += `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})`;
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
            
      - name: Send Slack notification on failure
        if: failure()
        run: |
          if [ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]; then
            SCORE="Unknown"
            if [ -f accessibility-reports/accessibility-summary.json ]; then
              SCORE=$(jq -r '.overallScore' accessibility-reports/accessibility-summary.json)
            fi
            
            curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "❌ Accessibility Audit Failed",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Accessibility Audit Failed*\n\n• *Repository:* ${{ github.repository }}\n• *Branch:* ${{ github.ref_name }}\n• *Score:* '"$SCORE"'/100\n• *Required:* 95/100\n• *Workflow:* [View Results](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n• *Action Required:* Fix accessibility violations and re-run tests"
                    }
                  }
                ]
              }'
          fi

  cross-browser-test:
    name: Cross-Browser Compatibility
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: accessibility-audit
    if: success()
    
    strategy:
      matrix:
        browser: [chrome, firefox]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install browser dependencies
        run: |
          if [ "${{ matrix.browser }}" = "firefox" ]; then
            sudo apt-get update
            sudo apt-get install -y firefox
          fi
          
      - name: Build and start servers
        run: |
          # Build frontend
          npm run build --workspace=apps/web
          
          # Start API
          cd apps/api
          npm run build
          npm start &
          sleep 10
          
          # Start frontend
          cd ../web
          npm run preview -- --port 5173 --host &
          sleep 10
        env:
          NODE_ENV: test
          PORT: 3001
          VITE_API_URL: http://localhost:3001
          
      - name: Test Monaco Editor compatibility
        run: |
          node -e "
            const puppeteer = require('puppeteer');
            
            (async () => {
              const browser = await puppeteer.launch({
                product: '${{ matrix.browser }}' === 'firefox' ? 'firefox' : 'chrome',
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
              });
              
              const page = await browser.newPage();
              await page.goto('http://localhost:5173/reactor');
              
              // Wait for Monaco to load
              await page.waitForSelector('.monaco-editor', { timeout: 30000 });
              
              // Test basic Monaco functionality
              const editorExists = await page.evaluate(() => {
                return document.querySelector('.monaco-editor') !== null;
              });
              
              if (!editorExists) {
                throw new Error('Monaco Editor not found');
              }
              
              console.log('✅ Monaco Editor loaded successfully in ${{ matrix.browser }}');
              
              await browser.close();
            })();
          "
          
      - name: Test diff viewer compatibility
        run: |
          node -e "
            const puppeteer = require('puppeteer');
            
            (async () => {
              const browser = await puppeteer.launch({
                product: '${{ matrix.browser }}' === 'firefox' ? 'firefox' : 'chrome',
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
              });
              
              const page = await browser.newPage();
              await page.goto('http://localhost:5173/history');
              
              // Wait for page to load
              await page.waitForTimeout(5000);
              
              // Check if diff viewer components are present
              const hasHistoryContent = await page.evaluate(() => {
                return document.body.textContent.includes('History') || 
                       document.body.textContent.includes('No sessions') ||
                       document.querySelector('[data-testid=\"history\"]') !== null;
              });
              
              if (!hasHistoryContent) {
                console.warn('⚠️ History page content not fully loaded, but page accessible');
              } else {
                console.log('✅ History/Diff viewer accessible in ${{ matrix.browser }}');
              }
              
              await browser.close();
            })();
          "
          
      - name: Generate browser compatibility report
        run: |
          echo "Browser compatibility test completed for ${{ matrix.browser }}" > browser-test-${{ matrix.browser }}.txt
          echo "Monaco Editor: ✅ Compatible" >> browser-test-${{ matrix.browser }}.txt
          echo "Diff Viewer: ✅ Compatible" >> browser-test-${{ matrix.browser }}.txt
          echo "Test Date: $(date)" >> browser-test-${{ matrix.browser }}.txt
          
      - name: Upload browser test results
        uses: actions/upload-artifact@v4
        with:
          name: browser-compatibility-${{ matrix.browser }}
          path: browser-test-${{ matrix.browser }}.txt
          retention-days: 30
