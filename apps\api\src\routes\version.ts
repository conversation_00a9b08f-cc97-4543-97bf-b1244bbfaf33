import { Router } from 'express';
import { readFileSync } from 'fs';
import { join } from 'path';
import { loggingService } from '../services/loggingService.js';

const router = Router();

interface VersionInfo {
  version: string;
  buildDate: string;
  gitCommit?: string;
  gitBranch?: string;
  environment: string;
  features: string[];
  apiVersion: string;
  frontendVersion?: string;
}

// Cache version info to avoid reading files repeatedly
let cachedVersionInfo: VersionInfo | null = null;
let lastVersionCheck = 0;
const VERSION_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get version information from package.json and environment
 */
function getVersionInfo(): VersionInfo {
  const now = Date.now();
  
  // Return cached version if still valid
  if (cachedVersionInfo && (now - lastVersionCheck) < VERSION_CACHE_TTL) {
    return cachedVersionInfo;
  }

  try {
    // Read package.json for version info
    const packageJsonPath = join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

    // Get git information from environment variables (set by CI/CD)
    const gitCommit = process.env.GIT_COMMIT || process.env.GITHUB_SHA;
    const gitBranch = process.env.GIT_BRANCH || process.env.GITHUB_REF_NAME;

    // Determine enabled features based on environment
    const features = [];
    if (process.env.FEATURE_GITHUB_INTEGRATION === 'true') features.push('github-integration');
    if (process.env.FEATURE_AI_PROVIDERS === 'true') features.push('ai-providers');
    if (process.env.FEATURE_QUEUE_MANAGEMENT === 'true') features.push('queue-management');
    if (process.env.FEATURE_OBSERVABILITY === 'true') features.push('observability');
    if (process.env.FEATURE_SECURITY_HARDENING === 'true') features.push('security-hardening');
    if (process.env.FEATURE_BILLING === 'true') features.push('billing');

    const versionInfo: VersionInfo = {
      version: packageJson.version || '1.0.0',
      buildDate: process.env.BUILD_DATE || new Date().toISOString(),
      gitCommit: gitCommit?.substring(0, 8), // Short commit hash
      gitBranch,
      environment: process.env.NODE_ENV || 'development',
      features,
      apiVersion: '1.0',
      frontendVersion: process.env.FRONTEND_VERSION
    };

    // Cache the version info
    cachedVersionInfo = versionInfo;
    lastVersionCheck = now;

    return versionInfo;
  } catch (error) {
    console.error('Failed to read version info:', error);
    
    // Return fallback version info
    const fallbackVersion: VersionInfo = {
      version: '1.0.0',
      buildDate: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      features: [],
      apiVersion: '1.0'
    };

    cachedVersionInfo = fallbackVersion;
    lastVersionCheck = now;

    return fallbackVersion;
  }
}

// GET /api/version - Get current version information
router.get('/version', async (req, res) => {
  try {
    const versionInfo = getVersionInfo();
    
    // Log version check (but not too frequently)
    if (Math.random() < 0.1) { // Log 10% of version checks
      await loggingService.log({
        level: 'info',
        message: 'Version information requested',
        service: 'version',
        metadata: {
          version: versionInfo.version,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        }
      });
    }

    res.json({
      success: true,
      data: versionInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Version endpoint error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to get version information',
      service: 'version',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get version information',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/version/check - Check for updates (compare with client version)
router.get('/version/check', async (req, res) => {
  try {
    const clientVersion = req.query.current as string;
    const currentVersion = getVersionInfo();
    
    if (!clientVersion) {
      return res.status(400).json({
        success: false,
        error: 'Client version is required',
        message: 'Please provide current version in query parameter'
      });
    }

    // Simple version comparison (in production, use semver)
    const updateAvailable = clientVersion !== currentVersion.version;
    
    const response = {
      success: true,
      updateAvailable,
      currentVersion: currentVersion.version,
      clientVersion,
      data: updateAvailable ? currentVersion : null,
      message: updateAvailable 
        ? `Update available: ${clientVersion} → ${currentVersion.version}`
        : 'You are running the latest version'
    };

    // Log update checks
    await loggingService.log({
      level: 'info',
      message: updateAvailable ? 'Update available for client' : 'Client is up to date',
      service: 'version',
      metadata: {
        clientVersion,
        currentVersion: currentVersion.version,
        updateAvailable,
        userAgent: req.get('User-Agent')
      }
    });

    res.json(response);
  } catch (error) {
    console.error('Version check error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to check for updates',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/version/changelog - Get changelog for version updates
router.get('/version/changelog', async (req, res) => {
  try {
    const fromVersion = req.query.from as string;
    const toVersion = req.query.to as string;
    
    // In a real application, you'd read this from a changelog file or database
    const changelog = {
      '1.0.0': {
        date: '2024-01-15',
        changes: [
          'Initial production release',
          'Complete reactor loop system',
          'GitHub integration',
          'AI provider support (OpenAI, Anthropic)',
          'Security hardening with Helmet and CSP',
          'Comprehensive logging and monitoring',
          'Queue management for load balancing',
          'Accessibility improvements',
          'Onboarding wizard'
        ],
        breaking: [],
        fixes: []
      },
      '1.1.0': {
        date: '2024-01-20',
        changes: [
          'Enhanced error handling',
          'Improved performance monitoring',
          'Additional security headers',
          'Better mobile responsiveness'
        ],
        breaking: [],
        fixes: [
          'Fixed Monaco editor loading issues',
          'Resolved queue management edge cases',
          'Fixed accessibility violations'
        ]
      }
    };

    const currentVersion = getVersionInfo();
    const requestedVersion = toVersion || currentVersion.version;
    
    const versionChangelog = changelog[requestedVersion as keyof typeof changelog];
    
    if (!versionChangelog) {
      return res.status(404).json({
        success: false,
        error: 'Changelog not found',
        message: `No changelog available for version ${requestedVersion}`
      });
    }

    res.json({
      success: true,
      version: requestedVersion,
      changelog: versionChangelog,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Changelog error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get changelog',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/version/update-notification - Record that user was notified of update
router.post('/version/update-notification', async (req, res) => {
  try {
    const { version, action } = req.body;
    
    if (!version || !action) {
      return res.status(400).json({
        success: false,
        error: 'Version and action are required'
      });
    }

    await loggingService.log({
      level: 'info',
      message: 'Update notification interaction',
      service: 'version',
      metadata: {
        version,
        action, // 'shown', 'dismissed', 'updated'
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }
    });

    res.json({
      success: true,
      message: 'Notification recorded'
    });
  } catch (error) {
    console.error('Update notification error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to record notification',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/version/health - Health check for version service
router.get('/version/health', async (req, res) => {
  try {
    const versionInfo = getVersionInfo();
    const isHealthy = !!versionInfo.version;
    
    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        version_info_available: !!versionInfo.version,
        package_json_readable: true,
        cache_functional: !!cachedVersionInfo
      },
      data: isHealthy ? {
        version: versionInfo.version,
        environment: versionInfo.environment,
        cacheAge: Date.now() - lastVersionCheck
      } : null
    });
  } catch (error) {
    console.error('Version health check error:', error);
    
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as versionRouter };
