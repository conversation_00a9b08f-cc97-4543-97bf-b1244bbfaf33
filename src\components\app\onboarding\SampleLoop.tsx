import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Zap,
  Code,
  Eye,
  Clock,
  Sparkles
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SampleLoopProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

interface LoopProgress {
  iteration: number;
  maxIterations: number;
  currentStep: 'planning' | 'critiquing' | 'complete';
  score?: number;
  message: string;
}

const SAMPLE_CODE = `function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price;
  }
  return total;
}`;

const SAMPLE_PROMPT = "Add error handling and input validation to this function";

export const SampleLoop = ({ onComplete, onSkip }: SampleLoopProps) => {
  const [isRunning, setIsRunning] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [progress, setProgress] = useState<LoopProgress | null>(null);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const runSampleLoop = async () => {
    setIsRunning(true);
    setError(null);
    setProgress(null);
    setResult(null);

    try {
      // Simulate the reactor loop with progress updates
      const mockProgress = [
        { iteration: 1, maxIterations: 3, currentStep: 'planning' as const, message: 'Plan Agent analyzing the code...' },
        { iteration: 1, maxIterations: 3, currentStep: 'critiquing' as const, score: 0.7, message: 'Critique Agent reviewing the plan...' },
        { iteration: 2, maxIterations: 3, currentStep: 'planning' as const, message: 'Plan Agent refining based on feedback...' },
        { iteration: 2, maxIterations: 3, currentStep: 'critiquing' as const, score: 0.85, message: 'Critique Agent evaluating improvements...' },
        { iteration: 3, maxIterations: 3, currentStep: 'planning' as const, message: 'Plan Agent finalizing the solution...' },
        { iteration: 3, maxIterations: 3, currentStep: 'critiquing' as const, score: 0.95, message: 'Critique Agent approving the final result...' },
        { iteration: 3, maxIterations: 3, currentStep: 'complete' as const, score: 0.95, message: 'Transformation complete!' }
      ];

      // Simulate progress updates
      for (const progressUpdate of mockProgress) {
        setProgress(progressUpdate);
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      // Simulate final result
      const mockResult = {
        success: true,
        data: {
          diff: "Added comprehensive error handling and input validation",
          score: 0.95,
          iterations: 3,
          logs: [
            { agent: 'planner', message: 'Analyzing function structure and identifying validation needs' },
            { agent: 'critic', message: 'Good start, but need more specific error messages' },
            { agent: 'planner', message: 'Added detailed error handling with specific messages' },
            { agent: 'critic', message: 'Excellent! All edge cases covered with clear error messages' }
          ]
        }
      };

      setResult(mockResult);
      setIsComplete(true);
      
      toast({
        title: 'Sample Loop Complete!',
        description: 'Your first reactor loop ran successfully with a score of 0.95',
      });

      // Auto-complete after showing results
      setTimeout(() => {
        onComplete?.();
      }, 3000);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Sample Loop Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getProgressPercentage = () => {
    if (!progress) return 0;
    const stepProgress = progress.currentStep === 'planning' ? 0.3 : 
                       progress.currentStep === 'critiquing' ? 0.7 : 1;
    return ((progress.iteration - 1) / progress.maxIterations + stepProgress / progress.maxIterations) * 100;
  };

  if (isComplete && result) {
    return (
      <div className="space-y-6">
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Congratulations!</strong> Your first reactor loop completed successfully with a score of {result.data.score}!
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-yellow-500" />
              Transformation Results
            </CardTitle>
            <CardDescription>
              Here's what the AI agents accomplished
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-green-600">{result.data.score}</p>
                <p className="text-sm text-muted-foreground">Final Score</p>
              </div>
              <div>
                <p className="text-2xl font-bold">{result.data.iterations}</p>
                <p className="text-sm text-muted-foreground">Iterations</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">100%</p>
                <p className="text-sm text-muted-foreground">Success Rate</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Transformation Summary:</h4>
              <p className="text-sm bg-muted p-3 rounded">{result.data.diff}</p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Agent Conversation:</h4>
              <div className="space-y-2">
                {result.data.logs.map((log: any, index: number) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <Badge variant="outline" className={
                      log.agent === 'planner' ? 'bg-blue-500/10 text-blue-700' : 'bg-purple-500/10 text-purple-700'
                    }>
                      {log.agent === 'planner' ? 'Plan' : 'Critic'}
                    </Badge>
                    <p className="flex-1">{log.message}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">You're All Set! 🎉</h3>
            <p className="text-muted-foreground">
              You've successfully completed the onboarding. You're ready to start transforming your own code!
            </p>
          </div>
          
          <Button
            onClick={onComplete}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Complete Onboarding
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto">
          <Zap className="h-8 w-8 text-purple-600" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-2">Test Your First Reactor Loop</h3>
          <p className="text-muted-foreground">
            Let's run a sample transformation to see the dual-agent system in action.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Code className="h-5 w-5" />
            Sample Code
          </CardTitle>
          <CardDescription>
            We'll transform this simple function to add error handling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-slate-900 text-slate-100 p-4 rounded-lg font-mono text-sm">
              <pre>{SAMPLE_CODE}</pre>
            </div>
            
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Prompt: "{SAMPLE_PROMPT}"
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {progress && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Loop Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Iteration {progress.iteration} of {progress.maxIterations}</span>
                <span>{Math.round(getProgressPercentage())}%</span>
              </div>
              <Progress value={getProgressPercentage()} className="h-2" />
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={
                progress.currentStep === 'planning' ? 'bg-blue-500/10 text-blue-700' :
                progress.currentStep === 'critiquing' ? 'bg-purple-500/10 text-purple-700' :
                'bg-green-500/10 text-green-700'
              }>
                {progress.currentStep === 'planning' ? 'Planning' :
                 progress.currentStep === 'critiquing' ? 'Critiquing' : 'Complete'}
              </Badge>
              {progress.score && (
                <Badge variant="outline">
                  Score: {progress.score}
                </Badge>
              )}
            </div>
            
            <p className="text-sm text-muted-foreground">{progress.message}</p>
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <Button
          onClick={runSampleLoop}
          disabled={isRunning}
          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
        >
          {isRunning ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Running Sample Loop...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Run Sample Loop
            </>
          )}
        </Button>

        <div className="text-center">
          <Button variant="ghost" onClick={onSkip}>
            Skip Test Run
          </Button>
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>What happens:</strong> The Plan Agent will analyze the code and suggest improvements, 
          then the Critique Agent will score and provide feedback. This continues until we reach a high-quality solution.
        </AlertDescription>
      </Alert>
    </div>
  );
};
