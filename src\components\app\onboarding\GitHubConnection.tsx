import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Github, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink,
  GitBranch,
  Users,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GitHubConnectionProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

interface GitHubUser {
  login: string;
  name: string;
  avatar_url: string;
  public_repos: number;
  followers: number;
}

export const GitHubConnection = ({ onComplete, onSkip }: GitHubConnectionProps) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const checkExistingConnection = async () => {
    try {
      // Check if user already has GitHub token
      const token = localStorage.getItem('github_token');
      if (token) {
        // Verify token is still valid
        const response = await fetch('https://api.github.com/user', {
          headers: {
            'Authorization': `token ${token}`,
          },
        });

        if (response.ok) {
          const userData = await response.json();
          setGithubUser(userData);
          setIsConnected(true);
        } else {
          // Token is invalid, remove it
          localStorage.removeItem('github_token');
        }
      }
    } catch (err) {
      console.error('Failed to check GitHub connection:', err);
    }
  };

  const initiateGitHubOAuth = () => {
    setIsConnecting(true);
    setError(null);

    const clientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
    
    if (!clientId) {
      setError('GitHub OAuth is not configured. Please contact support.');
      setIsConnecting(false);
      return;
    }

    const redirectUri = `${window.location.origin}/auth/github/callback`;
    const scope = 'repo,workflow,user:email';
    const state = Math.random().toString(36).substring(2, 15);
    
    // Store state for verification
    localStorage.setItem('github_oauth_state', state);

    const authUrl = `https://github.com/login/oauth/authorize?` +
      `client_id=${clientId}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `scope=${encodeURIComponent(scope)}&` +
      `state=${state}`;

    // Open OAuth in popup
    const popup = window.open(
      authUrl,
      'github-oauth',
      'width=600,height=700,scrollbars=yes,resizable=yes'
    );

    // Listen for popup messages
    const handleMessage = async (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;

      if (event.data.type === 'GITHUB_OAUTH_SUCCESS') {
        const { code, state: returnedState } = event.data;
        const storedState = localStorage.getItem('github_oauth_state');

        if (returnedState !== storedState) {
          setError('OAuth state mismatch. Please try again.');
          setIsConnecting(false);
          return;
        }

        try {
          // Exchange code for token
          const response = await fetch('/api/auth/github/callback', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ code }),
          });

          if (!response.ok) {
            throw new Error('Failed to exchange code for token');
          }

          const tokenData = await response.json();
          
          // Store token
          localStorage.setItem('github_token', tokenData.access_token);
          
          // Get user info
          const userResponse = await fetch('https://api.github.com/user', {
            headers: {
              'Authorization': `token ${tokenData.access_token}`,
            },
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            setGithubUser(userData);
            setIsConnected(true);
            
            toast({
              title: 'GitHub Connected!',
              description: `Successfully connected as ${userData.login}`,
            });

            // Auto-complete after a short delay
            setTimeout(() => {
              onComplete?.();
            }, 1500);
          }
        } catch (err) {
          setError('Failed to complete GitHub authentication');
          console.error('GitHub OAuth error:', err);
        } finally {
          setIsConnecting(false);
          localStorage.removeItem('github_oauth_state');
        }

        popup?.close();
      } else if (event.data.type === 'GITHUB_OAUTH_ERROR') {
        setError(event.data.error || 'GitHub authentication failed');
        setIsConnecting(false);
        popup?.close();
      }
    };

    window.addEventListener('message', handleMessage);

    // Check if popup was closed manually
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
        setIsConnecting(false);
      }
    }, 1000);
  };

  const handleDisconnect = () => {
    localStorage.removeItem('github_token');
    setIsConnected(false);
    setGithubUser(null);
    
    toast({
      title: 'GitHub Disconnected',
      description: 'Your GitHub account has been disconnected.',
    });
  };

  useEffect(() => {
    checkExistingConnection();
  }, []);

  if (isConnected && githubUser) {
    return (
      <div className="space-y-4">
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>GitHub Connected!</strong> You can now create pull requests automatically.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Github className="h-5 w-5" />
              Connected Account
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <img
                src={githubUser.avatar_url}
                alt={githubUser.login}
                className="w-12 h-12 rounded-full"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{githubUser.name || githubUser.login}</h4>
                  <Badge variant="outline" className="bg-green-500/10 text-green-700">
                    Connected
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">@{githubUser.login}</p>
                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <GitBranch className="h-3 w-3" />
                    {githubUser.public_repos} repos
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {githubUser.followers} followers
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-4 pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                Permissions: Repository access, Workflow management
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDisconnect}
              >
                Disconnect
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={onSkip}>
            Continue
          </Button>
          <Button
            onClick={onComplete}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Complete Setup
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
          <Github className="h-8 w-8 text-gray-600" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-2">Connect Your GitHub Account</h3>
          <p className="text-muted-foreground">
            Enable automatic pull request creation when your reactor loops complete successfully.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">What you'll get:</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="font-medium">Automatic PR Creation</p>
              <p className="text-sm text-muted-foreground">
                Create pull requests directly from successful transformations
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="font-medium">Repository Integration</p>
              <p className="text-sm text-muted-foreground">
                Seamlessly integrate with your existing GitHub workflow
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="font-medium">Secure Access</p>
              <p className="text-sm text-muted-foreground">
                OAuth-based authentication with minimal required permissions
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <Button
          onClick={initiateGitHubOAuth}
          disabled={isConnecting}
          className="w-full bg-gray-900 hover:bg-gray-800 text-white"
        >
          {isConnecting ? (
            <>
              <Settings className="h-4 w-4 mr-2 animate-spin" />
              Connecting to GitHub...
            </>
          ) : (
            <>
              <Github className="h-4 w-4 mr-2" />
              Connect with GitHub
            </>
          )}
        </Button>

        <div className="text-center">
          <Button variant="ghost" onClick={onSkip}>
            Skip GitHub Integration
          </Button>
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Optional Step:</strong> You can skip this and connect GitHub later in Settings. 
          This integration requires repository and workflow permissions.
          <a 
            href="https://docs.github.com/en/apps/oauth-apps/using-oauth-apps/authorizing-oauth-apps" 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 ml-2 text-blue-600 hover:underline"
          >
            Learn more <ExternalLink className="h-3 w-3" />
          </a>
        </AlertDescription>
      </Alert>
    </div>
  );
};
