<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telemetry Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b;
            color: #e2e8f0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background-color: #7f1d1d;
            border: 1px solid #ef4444;
        }
        .info {
            background-color: #1e3a8a;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>Telemetry Service Fix Test</h1>
    <div id="test-results"></div>

    <script type="module">
        // Mock Supabase for testing
        const mockSupabase = {
            auth: {
                getUser: () => Promise.resolve({ 
                    data: { user: null }, 
                    error: null 
                })
            },
            from: () => ({
                select: () => ({
                    eq: () => ({
                        single: () => Promise.resolve({ 
                            data: null, 
                            error: { code: 'PGRST116' } 
                        })
                    })
                }),
                insert: () => Promise.resolve({ error: null }),
                upsert: () => Promise.resolve({ error: null })
            })
        };

        // Mock isSupabaseConfigured
        const isSupabaseConfigured = () => true;

        // Simplified TelemetryService for testing
        class TestTelemetryService {
            constructor() {
                this._isEnabled = true;
                this._isInitialized = false;
                this.sessionId = 'test-session';
                this._initializationPromise = this.loadTelemetrySettings();
            }

            async loadTelemetrySettings() {
                try {
                    if (!isSupabaseConfigured()) {
                        console.warn('Supabase not configured, telemetry disabled');
                        this._isEnabled = false;
                        this._isInitialized = true;
                        return;
                    }

                    const { data: { user }, error: authError } = await mockSupabase.auth.getUser();
                    
                    if (authError) {
                        console.warn('Authentication error, using default telemetry settings:', authError);
                        this._isEnabled = true;
                        this._isInitialized = true;
                        return;
                    }

                    if (!user) {
                        this._isEnabled = true;
                        this._isInitialized = true;
                        return;
                    }

                    this._isEnabled = true;
                    this._isInitialized = true;
                } catch (error) {
                    console.warn('Error loading telemetry settings:', error);
                    this._isEnabled = true;
                    this._isInitialized = true;
                }
            }

            isEnabled() {
                return this._isEnabled;
            }

            isInitialized() {
                return this._isInitialized;
            }

            async waitForInitialization() {
                await this._initializationPromise;
            }
        }

        // Test functions
        function addTestResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('test-results').appendChild(div);
        }

        async function runTests() {
            addTestResult('Starting telemetry service tests...', 'info');

            try {
                // Test 1: Service initialization
                const telemetry = new TestTelemetryService();
                addTestResult('✓ TelemetryService created without errors', 'success');

                // Test 2: Check initial state
                const initialEnabled = telemetry.isEnabled();
                addTestResult(`✓ Initial isEnabled() returns: ${initialEnabled}`, 'success');

                const initialInitialized = telemetry.isInitialized();
                addTestResult(`✓ Initial isInitialized() returns: ${initialInitialized}`, 'success');

                // Test 3: Wait for initialization
                await telemetry.waitForInitialization();
                addTestResult('✓ waitForInitialization() completed without errors', 'success');

                // Test 4: Check post-initialization state
                const postInitEnabled = telemetry.isEnabled();
                const postInitInitialized = telemetry.isInitialized();
                addTestResult(`✓ Post-init isEnabled(): ${postInitEnabled}`, 'success');
                addTestResult(`✓ Post-init isInitialized(): ${postInitInitialized}`, 'success');

                if (postInitInitialized) {
                    addTestResult('✓ Service properly initialized', 'success');
                } else {
                    addTestResult('✗ Service failed to initialize', 'error');
                }

                addTestResult('All tests completed successfully!', 'success');

            } catch (error) {
                addTestResult(`✗ Test failed with error: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
