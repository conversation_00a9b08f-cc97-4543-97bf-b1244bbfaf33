import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const reactorLoopCounter = new Counter('reactor_loops_started');
const reactorLoopSuccessCounter = new Counter('reactor_loops_completed');

// Test configuration
export const options = {
  scenarios: {
    // Scenario 1: Gradual ramp-up to 50 parallel reactor loops
    reactor_load_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: '2m', target: 10 },  // Ramp up to 10 VUs over 2 minutes
        { duration: '5m', target: 25 },  // Ramp up to 25 VUs over 5 minutes
        { duration: '10m', target: 50 }, // Ramp up to 50 VUs over 10 minutes
        { duration: '10m', target: 50 }, // Stay at 50 VUs for 10 minutes
        { duration: '5m', target: 25 },  // Ramp down to 25 VUs over 5 minutes
        { duration: '2m', target: 0 },   // Ramp down to 0 VUs over 2 minutes
      ],
      gracefulRampDown: '30s',
    },
    
    // Scenario 2: Constant API load for monitoring endpoints
    api_monitoring: {
      executor: 'constant-vus',
      vus: 5,
      duration: '34m', // Run for the entire test duration
      exec: 'monitoringEndpoints',
    },
    
    // Scenario 3: Spike test to validate queue management
    spike_test: {
      executor: 'constant-arrival-rate',
      rate: 100, // 100 requests per second
      timeUnit: '1s',
      duration: '30s',
      preAllocatedVUs: 20,
      maxVUs: 50,
      startTime: '15m', // Start after 15 minutes
      exec: 'spikeTest',
    }
  },
  
  thresholds: {
    // Error rate should be less than 5%
    'errors': ['rate<0.05'],
    
    // 95% of requests should complete within 10 seconds
    'http_req_duration': ['p(95)<10000'],
    
    // Response time trend should be reasonable
    'response_time': ['p(95)<15000'],
    
    // At least 80% of reactor loops should complete successfully
    'reactor_loops_completed': ['count>0'],
    
    // Check that we can handle the target load
    'http_req_failed': ['rate<0.1'],
  }
};

// Test data
const testPrompts = [
  "Create a simple React component that displays a greeting message",
  "Add error handling to an Express.js API endpoint",
  "Implement a utility function for data validation",
  "Create a responsive CSS layout with flexbox",
  "Add unit tests for a JavaScript function",
  "Implement authentication middleware for Express",
  "Create a database migration script",
  "Add logging to an existing service",
  "Implement a caching layer for API responses",
  "Create a configuration management system"
];

// Configuration
const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:3001';
const API_KEY = __ENV.API_KEY || '';

// Headers
const headers = {
  'Content-Type': 'application/json',
  'Authorization': API_KEY ? `Bearer ${API_KEY}` : '',
};

// Main reactor loop test function
export default function () {
  group('Reactor Loop Load Test', function () {
    // Select a random prompt
    const prompt = testPrompts[Math.floor(Math.random() * testPrompts.length)];
    
    // Create reactor loop request
    const payload = JSON.stringify({
      prompt: prompt,
      context: {
        framework: 'react',
        language: 'typescript',
        testType: 'load-test'
      },
      maxLoops: 3 // Limit loops for load testing
    });
    
    reactorLoopCounter.add(1);
    
    // Start reactor loop
    const startTime = Date.now();
    const response = http.post(`${BASE_URL}/api/reactor/start`, payload, { headers });
    const endTime = Date.now();
    
    // Record metrics
    responseTime.add(endTime - startTime);
    errorRate.add(response.status !== 200);
    
    // Validate response
    const success = check(response, {
      'reactor loop started successfully': (r) => r.status === 200,
      'response has session ID': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.sessionId && body.sessionId.length > 0;
        } catch (e) {
          return false;
        }
      },
      'response time is acceptable': (r) => r.timings.duration < 30000, // 30 seconds
    });
    
    if (success && response.status === 200) {
      try {
        const responseBody = JSON.parse(response.body);
        const sessionId = responseBody.sessionId;
        
        if (sessionId) {
          // Monitor the reactor loop progress
          monitorReactorLoop(sessionId);
        }
      } catch (e) {
        console.error('Failed to parse response body:', e);
      }
    }
    
    // Add some delay between requests to simulate realistic usage
    sleep(Math.random() * 5 + 2); // 2-7 seconds
  });
}

// Monitor reactor loop progress
function monitorReactorLoop(sessionId) {
  const maxPolls = 20; // Maximum number of status checks
  let polls = 0;
  let completed = false;
  
  while (polls < maxPolls && !completed) {
    sleep(3); // Wait 3 seconds between polls
    
    const statusResponse = http.get(`${BASE_URL}/api/reactor/status/${sessionId}`, { headers });
    
    check(statusResponse, {
      'status check successful': (r) => r.status === 200,
    });
    
    if (statusResponse.status === 200) {
      try {
        const status = JSON.parse(statusResponse.body);
        
        if (status.status === 'completed') {
          reactorLoopSuccessCounter.add(1);
          completed = true;
        } else if (status.status === 'failed') {
          completed = true;
        }
      } catch (e) {
        console.error('Failed to parse status response:', e);
      }
    }
    
    polls++;
  }
}

// Monitoring endpoints test function
export function monitoringEndpoints() {
  group('Monitoring Endpoints', function () {
    // Test health endpoint
    const healthResponse = http.get(`${BASE_URL}/health`);
    check(healthResponse, {
      'health endpoint responds': (r) => r.status === 200,
    });
    
    // Test logs health endpoint
    const logsHealthResponse = http.get(`${BASE_URL}/api/logs/health`);
    check(logsHealthResponse, {
      'logs health endpoint responds': (r) => r.status === 200,
    });
    
    // Test metrics endpoint
    const metricsResponse = http.get(`${BASE_URL}/api/logs/metrics?timeRange=1h`, { headers });
    check(metricsResponse, {
      'metrics endpoint responds': (r) => r.status === 200,
    });
    
    sleep(10); // Check every 10 seconds
  });
}

// Spike test function to validate queue management
export function spikeTest() {
  group('Spike Test - Queue Management', function () {
    const payload = JSON.stringify({
      prompt: "Simple spike test prompt",
      context: { testType: 'spike-test' },
      maxLoops: 1
    });
    
    const response = http.post(`${BASE_URL}/api/reactor/start`, payload, { headers });
    
    check(response, {
      'spike request handled': (r) => r.status === 200 || r.status === 429, // Accept rate limiting
      'no server errors': (r) => r.status < 500,
    });
    
    // Check if queue management is working (should get 429 when overloaded)
    if (response.status === 429) {
      check(response, {
        'rate limiting message present': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.error && body.error.includes('rate limit');
          } catch (e) {
            return false;
          }
        }
      });
    }
  });
}

// Setup function - runs once before the test
export function setup() {
  console.log('🚀 Starting Metamorphic Reactor Load Test');
  console.log(`📍 Target URL: ${BASE_URL}`);
  console.log(`⏱️  Test Duration: ~34 minutes`);
  console.log(`👥 Max Concurrent Users: 50`);
  console.log(`📊 Scenarios: Reactor Load Test, API Monitoring, Spike Test`);
  
  // Verify API is accessible
  const healthCheck = http.get(`${BASE_URL}/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('✅ API health check passed');
  return { startTime: Date.now() };
}

// Teardown function - runs once after the test
export function teardown(data) {
  const duration = (Date.now() - data.startTime) / 1000 / 60; // minutes
  console.log(`🏁 Load test completed in ${duration.toFixed(2)} minutes`);
  console.log('📈 Check the test results and metrics above');
  console.log('🔍 Review API logs and monitoring dashboards for detailed analysis');
}
