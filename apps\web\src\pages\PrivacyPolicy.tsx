import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Separator } from '../components/ui/separator';
import { Badge } from '../components/ui/badge';
import { Shield, Eye, Database, Cookie, Mail, Lock } from 'lucide-react';

export const PrivacyPolicy: React.FC = () => {
  const lastUpdated = "January 15, 2024";
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Shield className="w-8 h-8 text-blue-600" />
            <h1 className="text-4xl font-bold">Privacy Policy</h1>
          </div>
          <p className="text-lg text-muted-foreground">
            How we collect, use, and protect your information
          </p>
          <Badge variant="secondary">
            Last updated: {lastUpdated}
          </Badge>
        </div>

        {/* Introduction */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="w-5 h-5" />
              <span>Introduction</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Metamorphic Reactor ("we," "our," or "us") is committed to protecting your privacy. 
              This Privacy Policy explains how we collect, use, disclose, and safeguard your information 
              when you use our code transformation service.
            </p>
            <p>
              By using our service, you agree to the collection and use of information in accordance 
              with this policy. If you do not agree with our policies and practices, do not use our service.
            </p>
          </CardContent>
        </Card>

        {/* Information We Collect */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Information We Collect</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Personal Information</h3>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>Email address (for account creation and communication)</li>
                <li>GitHub username and profile information (when connecting GitHub)</li>
                <li>API keys and tokens (encrypted and stored securely)</li>
                <li>Billing information (processed by Stripe, not stored by us)</li>
              </ul>
            </div>
            
            <Separator />
            
            <div>
              <h3 className="text-lg font-semibold mb-3">Usage Information</h3>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>Code prompts and transformation requests</li>
                <li>Generated code outputs and patches</li>
                <li>Usage patterns and feature interactions</li>
                <li>Error logs and performance metrics</li>
                <li>IP addresses and browser information</li>
              </ul>
            </div>
            
            <Separator />
            
            <div>
              <h3 className="text-lg font-semibold mb-3">Technical Information</h3>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>Device type, operating system, and browser version</li>
                <li>Session duration and page views</li>
                <li>Cookies and similar tracking technologies</li>
                <li>API usage statistics and rate limiting data</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* How We Use Information */}
        <Card>
          <CardHeader>
            <CardTitle>How We Use Your Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Service Provision</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Process code transformation requests</li>
                  <li>Generate and deliver code patches</li>
                  <li>Manage your account and preferences</li>
                  <li>Provide customer support</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Service Improvement</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Analyze usage patterns and performance</li>
                  <li>Improve AI model accuracy</li>
                  <li>Develop new features</li>
                  <li>Ensure security and prevent abuse</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Communication</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Send service updates and notifications</li>
                  <li>Respond to inquiries and support requests</li>
                  <li>Share important policy changes</li>
                  <li>Marketing (with your consent)</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Legal Compliance</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Comply with legal obligations</li>
                  <li>Protect our rights and property</li>
                  <li>Investigate potential violations</li>
                  <li>Ensure user safety</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lock className="w-5 h-5" />
              <span>Data Security</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              We implement appropriate technical and organizational security measures to protect 
              your personal information against unauthorized access, alteration, disclosure, or destruction.
            </p>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Technical Measures</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>End-to-end encryption for API keys</li>
                  <li>HTTPS/TLS for all communications</li>
                  <li>Regular security audits and updates</li>
                  <li>Access controls and authentication</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Organizational Measures</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Limited access on need-to-know basis</li>
                  <li>Employee security training</li>
                  <li>Incident response procedures</li>
                  <li>Regular backup and recovery testing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cookies and Tracking */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cookie className="w-5 h-5" />
              <span>Cookies and Tracking</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              We use cookies and similar technologies to enhance your experience, analyze usage, 
              and provide personalized content.
            </p>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">Essential Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  Required for basic functionality, authentication, and security. Cannot be disabled.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Analytics Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  Help us understand how you use our service to improve performance and user experience.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Preference Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  Remember your settings and preferences for a personalized experience.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Your Rights */}
        <Card>
          <CardHeader>
            <CardTitle>Your Rights (GDPR)</CardTitle>
            <CardDescription>
              If you are in the European Union, you have the following rights:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold">Access</h4>
                  <p className="text-sm text-muted-foreground">
                    Request a copy of your personal data
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold">Rectification</h4>
                  <p className="text-sm text-muted-foreground">
                    Correct inaccurate or incomplete data
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold">Erasure</h4>
                  <p className="text-sm text-muted-foreground">
                    Request deletion of your data
                  </p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold">Portability</h4>
                  <p className="text-sm text-muted-foreground">
                    Export your data in a machine-readable format
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold">Restriction</h4>
                  <p className="text-sm text-muted-foreground">
                    Limit how we process your data
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold">Objection</h4>
                  <p className="text-sm text-muted-foreground">
                    Object to certain types of processing
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Retention */}
        <Card>
          <CardHeader>
            <CardTitle>Data Retention</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              We retain your information only as long as necessary to provide our services 
              and comply with legal obligations.
            </p>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">Account Information</span>
                <Badge variant="outline">Until account deletion</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Code Transformations</span>
                <Badge variant="outline">90 days</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Usage Logs</span>
                <Badge variant="outline">12 months</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Billing Records</span>
                <Badge variant="outline">7 years (legal requirement)</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5" />
              <span>Contact Us</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              If you have questions about this Privacy Policy or want to exercise your rights, 
              please contact us:
            </p>
            
            <div className="space-y-2">
              <div>
                <span className="font-medium">Email:</span>
                <span className="ml-2"><EMAIL></span>
              </div>
              <div>
                <span className="font-medium">Data Protection Officer:</span>
                <span className="ml-2"><EMAIL></span>
              </div>
              <div>
                <span className="font-medium">Response Time:</span>
                <span className="ml-2">Within 30 days</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Changes to Policy */}
        <Card>
          <CardHeader>
            <CardTitle>Changes to This Policy</CardTitle>
          </CardHeader>
          <CardContent>
            <p>
              We may update this Privacy Policy from time to time. We will notify you of any 
              material changes by posting the new policy on this page and updating the "Last updated" 
              date. We encourage you to review this policy periodically.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
