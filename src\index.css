
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Semantic status colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --info: 221.2 83.2% 53.3%;
    --info-foreground: 210 40% 98%;

    /* Queue status colors */
    --status-queued: 221.2 83.2% 53.3%;
    --status-queued-foreground: 210 40% 98%;
    --status-running: 47.9 95.8% 53.1%;
    --status-running-foreground: 26 83.3% 14.1%;
    --status-completed: 142.1 76.2% 36.3%;
    --status-completed-foreground: 355.7 100% 97.3%;
    --status-failed: 0 84.2% 60.2%;
    --status-failed-foreground: 210 40% 98%;

    /* Agent colors */
    --agent-planner: 262.1 83.3% 57.8%;
    --agent-planner-foreground: 210 40% 98%;
    --agent-critic: 221.2 83.2% 53.3%;
    --agent-critic-foreground: 210 40% 98%;
    --agent-system: 215.4 16.3% 46.9%;
    --agent-system-foreground: 210 40% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark mode semantic status colors */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --info: 221.2 83.2% 53.3%;
    --info-foreground: 210 40% 98%;

    /* Dark mode queue status colors */
    --status-queued: 221.2 83.2% 53.3%;
    --status-queued-foreground: 210 40% 98%;
    --status-running: 47.9 95.8% 53.1%;
    --status-running-foreground: 26 83.3% 14.1%;
    --status-completed: 142.1 70.6% 45.3%;
    --status-completed-foreground: 355.7 100% 97.3%;
    --status-failed: 0 62.8% 30.6%;
    --status-failed-foreground: 210 40% 98%;

    /* Dark mode agent colors */
    --agent-planner: 262.1 83.3% 57.8%;
    --agent-planner-foreground: 210 40% 98%;
    --agent-critic: 221.2 83.2% 53.3%;
    --agent-critic-foreground: 210 40% 98%;
    --agent-system: 215.4 16.3% 46.9%;
    --agent-system-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  code, pre, .font-mono {
    font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
  }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Gradient text animations */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}
