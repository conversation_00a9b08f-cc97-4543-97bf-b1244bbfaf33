-- Metamorphic Reactor Database Schema
-- Complete schema with RLS policies for production use

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    github_username TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transformations table (main user sessions)
CREATE TABLE public.transformations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    prompt TEXT NOT NULL,
    max_iterations INTEGER NOT NULL DEFAULT 10,
    score_threshold DECIMAL(3,2) NOT NULL DEFAULT 0.95,
    final_score DECIMAL(3,2),
    final_patch JSONB,
    iterations_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'timeout')),
    github_pr_url TEXT,
    cost_usd DECIMAL(8,4) DEFAULT 0,
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Agent logs table for tracking dual-agent iterations
CREATE TABLE public.agent_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transformation_id UUID REFERENCES public.transformations(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    iteration INTEGER NOT NULL,
    plan TEXT NOT NULL,
    critique TEXT NOT NULL,
    score DECIMAL(3,2) NOT NULL CHECK (score >= 0 AND score <= 1),
    patch JSONB,
    tokens_used INTEGER DEFAULT 0,
    cost_usd DECIMAL(8,4) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings table for user preferences
CREATE TABLE public.settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    planner_model VARCHAR(50) DEFAULT 'gpt-4-turbo',
    critic_model VARCHAR(50) DEFAULT 'claude-3-sonnet',
    default_max_iterations INTEGER DEFAULT 10,
    default_score_threshold DECIMAL(3,2) DEFAULT 0.95,
    telemetry_enabled BOOLEAN DEFAULT true,
    auto_create_pr BOOLEAN DEFAULT false,
    github_repo_owner TEXT,
    github_repo_name TEXT,
    openai_api_key_encrypted TEXT,
    anthropic_api_key_encrypted TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- GitHub tokens table (encrypted storage)
CREATE TABLE public.github_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    access_token_encrypted TEXT NOT NULL,
    refresh_token_encrypted TEXT,
    github_user_id INTEGER,
    github_username TEXT,
    scopes TEXT[],
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Telemetry events table (anonymous usage analytics)
CREATE TABLE public.telemetry_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- Allow anonymous
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    session_id UUID,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_profiles_user_id ON public.profiles(id);
CREATE INDEX idx_transformations_user_id ON public.transformations(user_id);
CREATE INDEX idx_transformations_status ON public.transformations(status);
CREATE INDEX idx_transformations_created_at ON public.transformations(created_at);
CREATE INDEX idx_agent_logs_transformation_id ON public.agent_logs(transformation_id);
CREATE INDEX idx_agent_logs_user_id ON public.agent_logs(user_id);
CREATE INDEX idx_agent_logs_created_at ON public.agent_logs(created_at);
CREATE INDEX idx_settings_user_id ON public.settings(user_id);
CREATE INDEX idx_github_tokens_user_id ON public.github_tokens(user_id);
CREATE INDEX idx_telemetry_events_user_id ON public.telemetry_events(user_id);
CREATE INDEX idx_telemetry_events_event_type ON public.telemetry_events(event_type);
CREATE INDEX idx_telemetry_events_created_at ON public.telemetry_events(created_at);

-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transformations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.github_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.telemetry_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for transformations
CREATE POLICY "Users can view own transformations" ON public.transformations
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own transformations" ON public.transformations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own transformations" ON public.transformations
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own transformations" ON public.transformations
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for agent_logs
CREATE POLICY "Users can view own agent logs" ON public.agent_logs
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own agent logs" ON public.agent_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for settings
CREATE POLICY "Users can view own settings" ON public.settings
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own settings" ON public.settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own settings" ON public.settings
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for github_tokens
CREATE POLICY "Users can view own github tokens" ON public.github_tokens
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own github tokens" ON public.github_tokens
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own github tokens" ON public.github_tokens
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own github tokens" ON public.github_tokens
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for telemetry_events (more permissive for analytics)
CREATE POLICY "Users can view own telemetry" ON public.telemetry_events
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);
CREATE POLICY "Anyone can insert telemetry" ON public.telemetry_events
    FOR INSERT WITH CHECK (true);

-- Functions for encryption/decryption of sensitive data
CREATE OR REPLACE FUNCTION encrypt_secret(secret TEXT, key TEXT DEFAULT 'metamorphic-reactor-key')
RETURNS TEXT AS $$
BEGIN
    RETURN encode(pgp_sym_encrypt(secret, key), 'base64');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION decrypt_secret(encrypted_secret TEXT, key TEXT DEFAULT 'metamorphic-reactor-key')
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(decode(encrypted_secret, 'base64'), key);
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON public.settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_github_tokens_updated_at BEFORE UPDATE ON public.github_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
