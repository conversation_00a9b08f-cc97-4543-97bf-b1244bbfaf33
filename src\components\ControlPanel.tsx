
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Square, Zap } from "lucide-react";

interface ControlPanelProps {
  isRunning: boolean;
  onRunLoop: () => void;
  onStop: () => void;
}

export const ControlPanel = ({ isRunning, onRunLoop, onStop }: ControlPanelProps) => {
  return (
    <div className="flex items-center space-x-3">
      {isRunning ? (
        <>
          <Button
            onClick={onStop}
            variant="destructive"
            size="sm"
          >
            <Square className="w-4 h-4 mr-2" />
            Stop
          </Button>
          <Badge className="bg-status-running/20 text-status-running-foreground border-status-running/30 animate-pulse">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-status-running rounded-full"></div>
              <span>Running</span>
            </div>
          </Badge>
        </>
      ) : (
        <Button
          onClick={onRunLoop}
          className="bg-gradient-to-r from-agent-planner to-agent-critic hover:opacity-90 text-white"
          size="sm"
        >
          <Play className="w-4 h-4 mr-2" />
          Run Loop
        </Button>
      )}
    </div>
  );
};
