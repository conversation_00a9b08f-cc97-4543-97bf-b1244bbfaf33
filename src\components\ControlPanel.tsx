
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Square, Zap } from "lucide-react";

interface ControlPanelProps {
  isRunning: boolean;
  onRunLoop: () => void;
  onStop: () => void;
}

export const ControlPanel = ({ isRunning, onRunLoop, onStop }: ControlPanelProps) => {
  return (
    <div className="flex items-center space-x-3">
      {isRunning ? (
        <>
          <Button
            onClick={onStop}
            variant="destructive"
            size="sm"
            className="bg-red-600 hover:bg-red-700"
          >
            <Square className="w-4 h-4 mr-2" />
            Stop
          </Button>
          <Badge className="bg-green-600/20 text-green-300 border-green-500/30 animate-pulse">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Running</span>
            </div>
          </Badge>
        </>
      ) : (
        <Button
          onClick={onRunLoop}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
          size="sm"
        >
          <Play className="w-4 h-4 mr-2" />
          Run Loop
        </Button>
      )}
    </div>
  );
};
