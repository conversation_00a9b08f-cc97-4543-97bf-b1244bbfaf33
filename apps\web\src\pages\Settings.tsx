
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Key, Cpu, Github, Zap, Shield, Loader2, Save } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import { TelemetrySettings } from "@/components/TelemetrySettings";
import { telemetry } from "@/lib/telemetry";

interface UserSettings {
  id?: string;
  user_id?: string;
  planner_model: string;
  critic_model: string;
  default_max_iterations: number;
  default_score_threshold: number;
  telemetry_enabled: boolean;
  auto_create_pr: boolean;
  github_repo_owner?: string;
  github_repo_name?: string;
  openai_api_key_encrypted?: string;
  anthropic_api_key_encrypted?: string;
}

const Settings = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<UserSettings>({
    planner_model: "gpt-4-turbo",
    critic_model: "claude-3-sonnet",
    default_max_iterations: 10,
    default_score_threshold: 0.95,
    telemetry_enabled: true,
    auto_create_pr: false,
  });
  const [openaiApiKey, setOpenaiApiKey] = useState("");
  const [anthropicApiKey, setAnthropicApiKey] = useState("");
  const [googleApiKey, setGoogleApiKey] = useState("");

  useEffect(() => {
    loadSettings();
    telemetry.trackPageView('settings');
  }, []);

  const loadSettings = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please sign in to access settings");
        return;
      }

      const { data, error } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error loading settings:', error);
        toast.error("Failed to load settings");
        return;
      }

      if (data) {
        setSettings(data);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error("Failed to load settings");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please sign in to save settings");
        return;
      }

      // Encrypt API keys if provided
      let encryptedOpenAI = settings.openai_api_key_encrypted;
      let encryptedAnthropic = settings.anthropic_api_key_encrypted;

      if (openaiApiKey.trim()) {
        const { data: encrypted } = await supabase.rpc('encrypt_secret', { secret: openaiApiKey });
        encryptedOpenAI = encrypted;
      }

      if (anthropicApiKey.trim()) {
        const { data: encrypted } = await supabase.rpc('encrypt_secret', { secret: anthropicApiKey });
        encryptedAnthropic = encrypted;
      }

      const settingsData = {
        ...settings,
        user_id: user.id,
        openai_api_key_encrypted: encryptedOpenAI,
        anthropic_api_key_encrypted: encryptedAnthropic,
      };

      const { error } = await supabase
        .from('settings')
        .upsert(settingsData, { onConflict: 'user_id' });

      if (error) {
        console.error('Error saving settings:', error);
        toast.error("Failed to save settings");
        return;
      }

      toast.success("Settings saved successfully!");
      setOpenaiApiKey("");
      setAnthropicApiKey("");
      setGoogleApiKey("");
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error("Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
          <span className="text-white">Loading settings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Dashboard
            </Button>
            <h1 className="text-2xl font-bold text-white">Settings</h1>
            <Badge variant="secondary" className="bg-blue-600/20 text-blue-300 border-blue-500/30">
              Configuration
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        <div className="grid gap-8">
          {/* AI Models */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Cpu className="w-5 h-5 text-purple-400" />
                <CardTitle className="text-white">AI Models</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Configure the AI models for your dual-agent system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-slate-300">Planner Model</Label>
                  <Select
                    value={settings.planner_model}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, planner_model: value }))}
                  >
                    <SelectTrigger className="bg-slate-900 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900 border-slate-700">
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="gemini-2-pro">Gemini 2.0 Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-slate-300">Critic Model</Label>
                  <Select
                    value={settings.critic_model}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, critic_model: value }))}
                  >
                    <SelectTrigger className="bg-slate-900 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900 border-slate-700">
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="gemini-2-pro">Gemini 2.0 Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Keys */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Key className="w-5 h-5 text-yellow-400" />
                <CardTitle className="text-white">API Keys</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Securely store your API keys for AI model access
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-slate-300">OpenAI API Key</Label>
                <Input
                  type="password"
                  placeholder={settings.openai_api_key_encrypted ? "••••••••••••••••" : "sk-..."}
                  value={openaiApiKey}
                  onChange={(e) => setOpenaiApiKey(e.target.value)}
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
                {settings.openai_api_key_encrypted && (
                  <p className="text-xs text-green-400">✓ API key is securely stored</p>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-slate-300">Anthropic API Key</Label>
                <Input
                  type="password"
                  placeholder={settings.anthropic_api_key_encrypted ? "••••••••••••••••" : "sk-ant-..."}
                  value={anthropicApiKey}
                  onChange={(e) => setAnthropicApiKey(e.target.value)}
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
                {settings.anthropic_api_key_encrypted && (
                  <p className="text-xs text-green-400">✓ API key is securely stored</p>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-slate-300">Google AI API Key</Label>
                <Input
                  type="password"
                  placeholder="AIza..."
                  value={googleApiKey}
                  onChange={(e) => setGoogleApiKey(e.target.value)}
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
              </div>
            </CardContent>
          </Card>

          {/* Loop Configuration */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-green-400" />
                <CardTitle className="text-white">Loop Configuration</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Fine-tune the dual-agent iteration process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Maximum Iterations</Label>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {settings.default_max_iterations}
                  </Badge>
                </div>
                <Slider
                  value={[settings.default_max_iterations]}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, default_max_iterations: value[0] }))}
                  max={20}
                  min={1}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Score Threshold</Label>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {settings.default_score_threshold.toFixed(2)}
                  </Badge>
                </div>
                <Slider
                  value={[settings.default_score_threshold]}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, default_score_threshold: value[0] }))}
                  max={1}
                  min={0.5}
                  step={0.01}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* GitHub Integration */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Github className="w-5 h-5 text-green-400" />
                <CardTitle className="text-white">GitHub Integration</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Configure automatic Git operations and PR creation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Auto-commit approved patches</Label>
                  <p className="text-sm text-slate-500">
                    Automatically create branch and commit when score ≥ threshold
                  </p>
                </div>
                <Switch
                  checked={settings.auto_create_pr}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, auto_create_pr: checked }))}
                />
              </div>
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                <Github className="w-4 h-4 mr-2" />
                Connect GitHub Account
              </Button>
            </CardContent>
          </Card>

          {/* Privacy & Telemetry */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-blue-400" />
                <CardTitle className="text-white">Privacy & Telemetry</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Control data collection and usage analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Enable telemetry</Label>
                  <p className="text-sm text-slate-500">
                    Help improve Metamorphic Reactor by sharing anonymous usage data
                  </p>
                </div>
                <Switch
                  checked={settings.telemetry_enabled}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, telemetry_enabled: checked }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Telemetry Settings */}
          <TelemetrySettings />

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
