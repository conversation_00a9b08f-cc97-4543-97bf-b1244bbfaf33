import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  Users, 
  Zap, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface QueueItem {
  sessionId: string;
  prompt: string;
  priority: number;
  queuePosition: number;
  status: 'queued' | 'running' | 'completed' | 'failed';
  estimatedWaitTime?: number;
  createdAt: string;
}

interface QueueStatusData {
  totalItems: number;
  runningItems: number;
  queuedItems: number;
  completedToday: number;
  averageWaitTime: number;
  currentCapacity: number;
  maxCapacity: number;
  items: QueueItem[];
}

interface QueueStatusProps {
  onRefresh?: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const QueueStatus = ({ 
  onRefresh, 
  autoRefresh = true, 
  refreshInterval = 5000 
}: QueueStatusProps) => {
  const [queueData, setQueueData] = useState<QueueStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchQueueStatus = async () => {
    try {
      setError(null);
      const response = await fetch('/api/queue/status');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch queue status: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch queue status');
      }
      
      setQueueData(result.data);
      setLastUpdated(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Queue Status Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    fetchQueueStatus();
    onRefresh?.();
  };

  useEffect(() => {
    fetchQueueStatus();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchQueueStatus, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const getStatusIcon = (status: QueueItem['status']) => {
    switch (status) {
      case 'running':
        return <Zap className="h-4 w-4 text-status-running" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-status-completed" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-status-failed" />;
      default:
        return <Clock className="h-4 w-4 text-status-queued" />;
    }
  };

  const getStatusBadge = (status: QueueItem['status']) => {
    const variants = {
      queued: 'secondary',
      running: 'default',
      completed: 'default',
      failed: 'destructive'
    } as const;

    const colors = {
      queued: 'bg-status-queued/10 text-status-queued-foreground border-status-queued/20',
      running: 'bg-status-running/10 text-status-running-foreground border-status-running/20',
      completed: 'bg-status-completed/10 text-status-completed-foreground border-status-completed/20',
      failed: 'bg-status-failed/10 text-status-failed-foreground border-status-failed/20'
    };

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    );
  };

  const formatWaitTime = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  if (loading && !queueData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Queue Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-8 w-12" />
              </div>
            ))}
          </div>
          <Skeleton className="h-32 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Queue Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="ml-2"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!queueData) return null;

  const capacityPercentage = (queueData.currentCapacity / queueData.maxCapacity) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Queue Status
            </CardTitle>
            <CardDescription>
              Real-time reactor loop queue monitoring
              {lastUpdated && (
                <span className="ml-2 text-xs">
                  Updated {lastUpdated.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Queue Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Total Items</p>
            <p className="text-2xl font-bold">{queueData.totalItems}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Running</p>
            <p className="text-2xl font-bold text-status-running">{queueData.runningItems}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Queued</p>
            <p className="text-2xl font-bold text-status-queued">{queueData.queuedItems}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Completed Today</p>
            <p className="text-2xl font-bold text-status-completed">{queueData.completedToday}</p>
          </div>
        </div>

        {/* Capacity Indicator */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Capacity Usage</span>
            <span>{queueData.currentCapacity}/{queueData.maxCapacity}</span>
          </div>
          <Progress value={capacityPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground">
            Average wait time: {formatWaitTime(queueData.averageWaitTime)}
          </p>
        </div>

        {/* Queue Items */}
        {queueData.items.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <h4 className="font-medium">Recent Queue Items</h4>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {queueData.items.map((item) => (
                <div
                  key={item.sessionId}
                  className="flex items-center justify-between p-3 border rounded-lg bg-card"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {getStatusBadge(item.status)}
                      {item.queuePosition > 0 && (
                        <Badge variant="outline" className="text-xs">
                          #{item.queuePosition}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm font-medium truncate">{item.prompt}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(item.createdAt).toLocaleString()}
                      {item.estimatedWaitTime && item.status === 'queued' && (
                        <span className="ml-2">
                          Est. wait: {formatWaitTime(item.estimatedWaitTime)}
                        </span>
                      )}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">Priority</p>
                    <p className="text-sm font-medium">{item.priority}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No items in queue</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
