import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Settings, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Zap,
  GitBranch
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface QueueManagerProps {
  onQueueSubmit?: (sessionId: string) => void;
  disabled?: boolean;
}

interface QueueFormData {
  prompt: string;
  maxLoops: number;
  priority: number;
  createPR: boolean;
  context?: Record<string, any>;
}

export const QueueManager = ({ onQueueSubmit, disabled = false }: QueueManagerProps) => {
  const [formData, setFormData] = useState<QueueFormData>({
    prompt: '',
    maxLoops: 5,
    priority: 0,
    createPR: false,
    context: {}
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSubmission, setLastSubmission] = useState<{
    sessionId: string;
    queuePosition: number;
    timestamp: Date;
  } | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.prompt.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please enter a prompt for the reactor loop.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/queue/reactor/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: formData.prompt.trim(),
          maxLoops: formData.maxLoops,
          priority: formData.priority,
          createPR: formData.createPR,
          context: formData.context
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to queue reactor loop: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to queue reactor loop');
      }

      setLastSubmission({
        sessionId: result.sessionId,
        queuePosition: result.queuePosition,
        timestamp: new Date()
      });

      // Reset form
      setFormData({
        prompt: '',
        maxLoops: 5,
        priority: 0,
        createPR: false,
        context: {}
      });

      toast({
        title: 'Reactor Loop Queued',
        description: result.message,
      });

      onQueueSubmit?.(result.sessionId);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast({
        title: 'Queue Submission Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof QueueFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getPriorityLabel = (priority: number) => {
    if (priority >= 8) return { label: 'Critical', color: 'bg-destructive/10 text-destructive-foreground border-destructive/20' };
    if (priority >= 6) return { label: 'High', color: 'bg-warning/10 text-warning-foreground border-warning/20' };
    if (priority >= 4) return { label: 'Medium', color: 'bg-warning/10 text-warning-foreground border-warning/20' };
    if (priority >= 2) return { label: 'Low', color: 'bg-info/10 text-info-foreground border-info/20' };
    return { label: 'Normal', color: 'bg-muted/10 text-muted-foreground border-muted/20' };
  };

  const priorityInfo = getPriorityLabel(formData.priority);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          Queue Reactor Loop
        </CardTitle>
        <CardDescription>
          Submit a new reactor loop to the processing queue with priority settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Prompt Input */}
          <div className="space-y-2">
            <Label htmlFor="prompt">
              Transformation Prompt <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="prompt"
              placeholder="Describe the code transformation you want to perform..."
              value={formData.prompt}
              onChange={(e) => handleInputChange('prompt', e.target.value)}
              disabled={disabled || isSubmitting}
              className="min-h-[100px]"
              required
            />
            <p className="text-xs text-muted-foreground">
              Be specific about what changes you want the AI agents to make to your code.
            </p>
          </div>

          {/* Configuration Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Max Loops */}
            <div className="space-y-2">
              <Label htmlFor="maxLoops">Max Iterations</Label>
              <Select
                value={formData.maxLoops.toString()}
                onValueChange={(value) => handleInputChange('maxLoops', parseInt(value))}
                disabled={disabled || isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 iteration</SelectItem>
                  <SelectItem value="3">3 iterations</SelectItem>
                  <SelectItem value="5">5 iterations (recommended)</SelectItem>
                  <SelectItem value="10">10 iterations</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Priority */}
            <div className="space-y-2">
              <Label htmlFor="priority">
                Priority Level
                <Badge variant="outline" className={`ml-2 ${priorityInfo.color}`}>
                  {priorityInfo.label}
                </Badge>
              </Label>
              <Input
                id="priority"
                type="range"
                min="0"
                max="10"
                step="1"
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                disabled={disabled || isSubmitting}
                className="cursor-pointer"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Normal (0)</span>
                <span>Critical (10)</span>
              </div>
            </div>
          </div>

          {/* Options */}
          <div className="space-y-4">
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="createPR" className="flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  Create GitHub PR
                </Label>
                <p className="text-sm text-muted-foreground">
                  Automatically create a pull request when the loop completes successfully
                </p>
              </div>
              <Switch
                id="createPR"
                checked={formData.createPR}
                onCheckedChange={(checked) => handleInputChange('createPR', checked)}
                disabled={disabled || isSubmitting}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex items-center gap-4">
            <Button
              type="submit"
              disabled={disabled || isSubmitting || !formData.prompt.trim()}
              className="bg-gradient-to-r from-agent-planner to-agent-critic hover:opacity-90"
            >
              {isSubmitting ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Queueing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Queue Reactor Loop
                </>
              )}
            </Button>

            {formData.prompt.trim() && (
              <div className="text-sm text-muted-foreground">
                Estimated queue time: ~{Math.max(1, formData.priority > 5 ? 2 : 5)}min
              </div>
            )}
          </div>

          {/* Last Submission Status */}
          {lastSubmission && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Successfully queued!</strong> Session ID: {lastSubmission.sessionId}
                {lastSubmission.queuePosition > 0 && (
                  <span className="ml-2">
                    Queue position: #{lastSubmission.queuePosition}
                  </span>
                )}
                <div className="text-xs text-muted-foreground mt-1">
                  Submitted at {lastSubmission.timestamp.toLocaleTimeString()}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Help Text */}
          <Alert>
            <Settings className="h-4 w-4" />
            <AlertDescription>
              <strong>Queue Tips:</strong> Higher priority items (6-10) are processed first. 
              Normal priority items are processed in FIFO order. Creating a PR requires GitHub authentication.
            </AlertDescription>
          </Alert>
        </form>
      </CardContent>
    </Card>
  );
};
