import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Separator } from '../components/ui/separator';
import { Badge } from '../components/ui/badge';
import { But<PERSON> } from '../components/ui/button';
import { <PERSON><PERSON>, Shield, BarChart3, Palette, Target, Settings } from 'lucide-react';

export const CookiePolicy: React.FC = () => {
  const lastUpdated = "January 15, 2024";
  
  const openCookieSettings = () => {
    // This would trigger the cookie consent dialog
    localStorage.removeItem('cookie-consent-given');
    window.location.reload();
  };
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Cookie className="w-8 h-8 text-blue-600" />
            <h1 className="text-4xl font-bold">Cookie Policy</h1>
          </div>
          <p className="text-lg text-muted-foreground">
            How we use cookies and similar technologies
          </p>
          <Badge variant="secondary">
            Last updated: {lastUpdated}
          </Badge>
        </div>

        {/* Quick Actions */}
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
              <div>
                <h3 className="font-semibold mb-1">Manage Your Cookie Preferences</h3>
                <p className="text-sm text-muted-foreground">
                  You can change your cookie settings at any time
                </p>
              </div>
              <Button onClick={openCookieSettings}>
                <Settings className="w-4 h-4 mr-2" />
                Cookie Settings
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* What Are Cookies */}
        <Card>
          <CardHeader>
            <CardTitle>What Are Cookies?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Cookies are small text files that are stored on your device when you visit a website. 
              They help websites remember information about your visit, which can make your next 
              visit easier and the site more useful to you.
            </p>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">First-Party Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  Set directly by our website. We have full control over these cookies 
                  and use them for essential functionality and analytics.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Third-Party Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  Set by external services we use, such as analytics providers or 
                  advertising networks. We have limited control over these cookies.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cookie Categories */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Types of Cookies We Use</h2>
          
          {/* Essential Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-600" />
                <span>Essential Cookies</span>
                <Badge variant="secondary">Always Active</Badge>
              </CardTitle>
              <CardDescription>
                Required for basic website functionality and security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                These cookies are necessary for the website to function properly. They enable 
                core functionality such as security, network management, and accessibility.
              </p>
              
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium">Authentication Cookies</h5>
                  <p className="text-xs text-muted-foreground">
                    Keep you logged in and maintain your session securely
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">session_token</Badge>
                    <Badge variant="outline" className="text-xs">csrf_token</Badge>
                    <Badge variant="outline" className="text-xs">auth_state</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Security Cookies</h5>
                  <p className="text-xs text-muted-foreground">
                    Protect against cross-site request forgery and other security threats
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">security_token</Badge>
                    <Badge variant="outline" className="text-xs">rate_limit</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Load Balancing</h5>
                  <p className="text-xs text-muted-foreground">
                    Ensure optimal performance by distributing traffic across servers
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">server_id</Badge>
                    <Badge variant="outline" className="text-xs">load_balancer</Badge>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted p-3 rounded-lg">
                <p className="text-xs">
                  <strong>Retention:</strong> Session cookies are deleted when you close your browser. 
                  Persistent cookies are kept for up to 30 days.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Analytics Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <span>Analytics Cookies</span>
                <Badge variant="outline">Optional</Badge>
              </CardTitle>
              <CardDescription>
                Help us understand how visitors use our website
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                These cookies collect information about how you use our website, such as 
                which pages you visit and how long you spend on each page. This helps us 
                improve our website and user experience.
              </p>
              
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium">Google Analytics</h5>
                  <p className="text-xs text-muted-foreground">
                    Tracks page views, user interactions, and website performance
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">_ga</Badge>
                    <Badge variant="outline" className="text-xs">_ga_*</Badge>
                    <Badge variant="outline" className="text-xs">_gid</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Usage Analytics</h5>
                  <p className="text-xs text-muted-foreground">
                    Monitor feature usage and user behavior patterns
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">usage_stats</Badge>
                    <Badge variant="outline" className="text-xs">feature_tracking</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Performance Monitoring</h5>
                  <p className="text-xs text-muted-foreground">
                    Track page load times and identify performance issues
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">perf_metrics</Badge>
                    <Badge variant="outline" className="text-xs">error_tracking</Badge>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted p-3 rounded-lg">
                <p className="text-xs">
                  <strong>Retention:</strong> Up to 26 months. Data is anonymized and aggregated.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Preference Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="w-5 h-5 text-purple-600" />
                <span>Preference Cookies</span>
                <Badge variant="outline">Optional</Badge>
              </CardTitle>
              <CardDescription>
                Remember your settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                These cookies remember your preferences and settings to provide a 
                personalized experience when you return to our website.
              </p>
              
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium">Theme Preferences</h5>
                  <p className="text-xs text-muted-foreground">
                    Remember your dark/light mode preference
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">theme</Badge>
                    <Badge variant="outline" className="text-xs">color_scheme</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Language Settings</h5>
                  <p className="text-xs text-muted-foreground">
                    Store your preferred language and locale
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">language</Badge>
                    <Badge variant="outline" className="text-xs">locale</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">UI Customizations</h5>
                  <p className="text-xs text-muted-foreground">
                    Remember your interface preferences and layout choices
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">ui_preferences</Badge>
                    <Badge variant="outline" className="text-xs">layout_settings</Badge>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted p-3 rounded-lg">
                <p className="text-xs">
                  <strong>Retention:</strong> Up to 12 months or until you change your preferences.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Marketing Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-orange-600" />
                <span>Marketing Cookies</span>
                <Badge variant="outline">Optional</Badge>
              </CardTitle>
              <CardDescription>
                Used for advertising and marketing purposes
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                These cookies are used to deliver relevant advertisements and measure 
                the effectiveness of advertising campaigns. They may be set by our 
                advertising partners.
              </p>
              
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium">Advertising Targeting</h5>
                  <p className="text-xs text-muted-foreground">
                    Show relevant ads based on your interests and behavior
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">ad_targeting</Badge>
                    <Badge variant="outline" className="text-xs">interest_profile</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Conversion Tracking</h5>
                  <p className="text-xs text-muted-foreground">
                    Measure the effectiveness of our advertising campaigns
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">conversion_id</Badge>
                    <Badge variant="outline" className="text-xs">campaign_tracking</Badge>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium">Social Media Integration</h5>
                  <p className="text-xs text-muted-foreground">
                    Enable social media sharing and track social interactions
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">social_tracking</Badge>
                    <Badge variant="outline" className="text-xs">share_buttons</Badge>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted p-3 rounded-lg">
                <p className="text-xs">
                  <strong>Retention:</strong> Up to 13 months. May be shared with advertising partners.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Managing Cookies */}
        <Card>
          <CardHeader>
            <CardTitle>Managing Your Cookie Preferences</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">On Our Website</h4>
              <p className="text-sm mb-3">
                You can manage your cookie preferences using our cookie consent banner 
                or by clicking the "Cookie Settings" button above.
              </p>
              <Button onClick={openCookieSettings} size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Open Cookie Settings
              </Button>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2">In Your Browser</h4>
              <p className="text-sm mb-3">
                You can also control cookies through your browser settings. Note that 
                disabling essential cookies may affect website functionality.
              </p>
              
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h5 className="font-medium">Chrome</h5>
                  <p className="text-xs text-muted-foreground">
                    Settings → Privacy and security → Cookies and other site data
                  </p>
                </div>
                <div>
                  <h5 className="font-medium">Firefox</h5>
                  <p className="text-xs text-muted-foreground">
                    Settings → Privacy & Security → Cookies and Site Data
                  </p>
                </div>
                <div>
                  <h5 className="font-medium">Safari</h5>
                  <p className="text-xs text-muted-foreground">
                    Preferences → Privacy → Manage Website Data
                  </p>
                </div>
                <div>
                  <h5 className="font-medium">Edge</h5>
                  <p className="text-xs text-muted-foreground">
                    Settings → Cookies and site permissions → Cookies and site data
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Questions About Cookies?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm mb-4">
              If you have questions about our use of cookies or this Cookie Policy, 
              please contact us:
            </p>
            
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Email:</span>
                <span className="ml-2"><EMAIL></span>
              </div>
              <div>
                <span className="font-medium">Subject:</span>
                <span className="ml-2">Cookie Policy Inquiry</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
