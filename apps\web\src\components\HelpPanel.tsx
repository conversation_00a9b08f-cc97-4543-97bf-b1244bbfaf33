
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Keyboard, Zap } from "lucide-react";

const shortcuts = [
  { keys: ['Ctrl', 'Enter'], description: 'Run transformation loop', mac: ['⌘', '↵'] },
  { keys: ['Esc'], description: 'Stop transformation', mac: ['Esc'] },
  { keys: ['Ctrl', 'Shift', 'K'], description: 'Clear logs', mac: ['⌘', '⇧', 'K'] },
  { keys: ['Ctrl', 'Shift', 'A'], description: 'Apply changes', mac: ['⌘', '⇧', 'A'] }
];

export const HelpPanel = () => {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-2">
          <Keyboard className="w-4 h-4 text-purple-400" />
          <CardTitle className="text-sm text-white">Keyboard Shortcuts</CardTitle>
        </div>
        <CardDescription className="text-xs text-slate-400">
          Power user shortcuts for faster workflow
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-xs text-slate-300">{shortcut.description}</span>
            <div className="flex items-center space-x-1">
              {(isMac ? shortcut.mac : shortcut.keys).map((key, keyIndex) => (
                <Badge
                  key={keyIndex}
                  variant="outline"
                  className="text-xs px-1.5 py-0.5 border-slate-600 text-slate-300"
                >
                  {key}
                </Badge>
              ))}
            </div>
          </div>
        ))}
        
        <div className="mt-3 pt-2 border-t border-slate-700">
          <div className="flex items-center space-x-1 text-xs text-slate-500">
            <Zap className="w-3 h-3" />
            <span>Shortcuts work when editor is not focused</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
