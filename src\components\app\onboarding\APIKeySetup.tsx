import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  EyeOff, 
  Key, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Info
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface APIKeySetupProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

interface APIKeyData {
  openai: string;
  anthropic: string;
  github: string;
}

interface ValidationResult {
  provider: string;
  status: 'saved' | 'failed';
  error?: string;
}

export const APIKeySetup = ({ onComplete, onSkip }: APIKeySetupProps) => {
  const [apiKeys, setApiKeys] = useState<APIKeyData>({
    openai: '',
    anthropic: '',
    github: ''
  });
  const [showKeys, setShowKeys] = useState({
    openai: false,
    anthropic: false,
    github: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const { toast } = useToast();

  const handleKeyChange = (provider: keyof APIKeyData, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [provider]: value
    }));
    // Clear validation results when user types
    setValidationResults([]);
  };

  const toggleKeyVisibility = (provider: keyof APIKeyData) => {
    setShowKeys(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const validateAndSaveKeys = async () => {
    const keysToSave = Object.entries(apiKeys).filter(([_, value]) => value.trim());
    
    if (keysToSave.length === 0) {
      toast({
        title: 'No API Keys Provided',
        description: 'Please enter at least one API key to continue.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    setValidationResults([]);

    try {
      const response = await fetch('/api/onboarding/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase_token')}`,
        },
        body: JSON.stringify({
          openai: apiKeys.openai.trim() || undefined,
          anthropic: apiKeys.anthropic.trim() || undefined,
          github: apiKeys.github.trim() || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save API keys: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setValidationResults(result.results);
        
        const successCount = result.results.filter((r: ValidationResult) => r.status === 'saved').length;
        const failCount = result.results.filter((r: ValidationResult) => r.status === 'failed').length;

        if (successCount > 0) {
          toast({
            title: 'API Keys Saved',
            description: `Successfully saved ${successCount} API key(s)${failCount > 0 ? `, ${failCount} failed` : ''}.`,
          });

          // Auto-complete if at least one key was saved successfully
          setTimeout(() => {
            onComplete?.();
          }, 1500);
        } else {
          toast({
            title: 'Save Failed',
            description: 'No API keys could be saved. Please check your keys and try again.',
            variant: 'destructive',
          });
        }
      } else {
        throw new Error(result.error || 'Failed to save API keys');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast({
        title: 'Save Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getProviderInfo = (provider: keyof APIKeyData) => {
    const info = {
      openai: {
        name: 'OpenAI',
        description: 'Powers the Plan Agent with GPT-4 models',
        placeholder: 'sk-...',
        helpUrl: 'https://platform.openai.com/api-keys',
        required: true
      },
      anthropic: {
        name: 'Anthropic',
        description: 'Powers the Critique Agent with Claude models',
        placeholder: 'sk-ant-...',
        helpUrl: 'https://console.anthropic.com/account/keys',
        required: true
      },
      github: {
        name: 'GitHub',
        description: 'Enables automatic PR creation (optional)',
        placeholder: 'ghp_...',
        helpUrl: 'https://github.com/settings/tokens',
        required: false
      }
    };
    return info[provider];
  };

  const renderKeyInput = (provider: keyof APIKeyData) => {
    const info = getProviderInfo(provider);
    const validation = validationResults.find(r => r.provider === provider);
    const hasValue = apiKeys[provider].trim().length > 0;

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor={provider} className="font-medium">
              {info.name}
              {info.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {!info.required && (
              <Badge variant="outline" className="text-xs">Optional</Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            <a href={info.helpUrl} target="_blank" rel="noopener noreferrer">
              Get API Key <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </Button>
        </div>

        <p className="text-sm text-muted-foreground">{info.description}</p>

        <div className="relative">
          <Input
            id={provider}
            type={showKeys[provider] ? 'text' : 'password'}
            placeholder={info.placeholder}
            value={apiKeys[provider]}
            onChange={(e) => handleKeyChange(provider, e.target.value)}
            disabled={isSubmitting}
            className={`pr-20 ${
              validation?.status === 'saved' ? 'border-green-500' :
              validation?.status === 'failed' ? 'border-red-500' : ''
            }`}
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {validation && (
              validation.status === 'saved' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )
            )}
            {hasValue && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => toggleKeyVisibility(provider)}
                className="h-6 w-6 p-0"
              >
                {showKeys[provider] ? (
                  <EyeOff className="h-3 w-3" />
                ) : (
                  <Eye className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>

        {validation?.status === 'failed' && validation.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {validation.error}
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  const hasAnyKeys = Object.values(apiKeys).some(key => key.trim().length > 0);
  const hasRequiredKeys = apiKeys.openai.trim() || apiKeys.anthropic.trim();

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Secure Storage:</strong> Your API keys are encrypted and stored securely. 
          You need at least one AI provider (OpenAI or Anthropic) to use the reactor loop.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="setup" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="setup">Setup Keys</TabsTrigger>
          <TabsTrigger value="help">Help & Info</TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="space-y-6">
          <div className="space-y-6">
            {renderKeyInput('openai')}
            {renderKeyInput('anthropic')}
            {renderKeyInput('github')}
          </div>

          <div className="flex items-center justify-between pt-4">
            <Button
              variant="ghost"
              onClick={onSkip}
              disabled={isSubmitting}
            >
              Skip for Now
            </Button>

            <Button
              onClick={validateAndSaveKeys}
              disabled={!hasAnyKeys || isSubmitting}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {isSubmitting ? (
                <>
                  <Key className="h-4 w-4 mr-2 animate-pulse" />
                  Saving Keys...
                </>
              ) : (
                <>
                  <Key className="h-4 w-4 mr-2" />
                  Save & Continue
                </>
              )}
            </Button>
          </div>

          {!hasRequiredKeys && hasAnyKeys && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                You need at least one AI provider key (OpenAI or Anthropic) to run reactor loops.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="help" className="space-y-4">
          <div className="grid gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">OpenAI API Key</CardTitle>
                <CardDescription>
                  Required for the Plan Agent (GPT-4 models)
                </CardDescription>
              </CardHeader>
              <CardContent className="text-sm space-y-2">
                <p>1. Visit <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI API Keys</a></p>
                <p>2. Click "Create new secret key"</p>
                <p>3. Copy the key (starts with "sk-")</p>
                <p>4. Paste it in the OpenAI field above</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Anthropic API Key</CardTitle>
                <CardDescription>
                  Required for the Critique Agent (Claude models)
                </CardDescription>
              </CardHeader>
              <CardContent className="text-sm space-y-2">
                <p>1. Visit <a href="https://console.anthropic.com/account/keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Anthropic Console</a></p>
                <p>2. Click "Create Key"</p>
                <p>3. Copy the key (starts with "sk-ant-")</p>
                <p>4. Paste it in the Anthropic field above</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">GitHub Token (Optional)</CardTitle>
                <CardDescription>
                  Enables automatic PR creation from successful loops
                </CardDescription>
              </CardHeader>
              <CardContent className="text-sm space-y-2">
                <p>1. Visit <a href="https://github.com/settings/tokens" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">GitHub Tokens</a></p>
                <p>2. Click "Generate new token (classic)"</p>
                <p>3. Select scopes: repo, workflow</p>
                <p>4. Copy the token (starts with "ghp_")</p>
                <p>5. Paste it in the GitHub field above</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
