# 🗺️ API Route Map - Complete Backend Endpoint Inventory

## Overview
Comprehensive mapping of all backend API endpoints in the Metamorphic Reactor application.

**Base URL**: `http://localhost:3001` (development) | `https://api.metamorphic-reactor.com` (production)

---

## 🤖 Reactor Loop Routes (`/api/loop`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/loop` | Start new reactor loop | `{ prompt: string, maxLoops?: number, context?: object, createPR?: boolean }` | `{ success: boolean, data: { diff, score, iterations, logs, prUrl } }` |
| `GET` | `/api/loop/:sessionId` | Get session details | `sessionId: UUID` | `{ success: boolean, data: SessionData }` |
| `POST` | `/api/loop/stream` | Start streaming loop (SSE) | Same as `/api/loop` | Server-Sent Events stream |

---

## 📋 Queue Management Routes (`/api/queue`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/queue/reactor/start` | Start queued reactor loop | `{ prompt: string, context?: object, maxLoops?: number, priority?: number, createPR?: boolean }` | `{ success: boolean, sessionId: string, queuePosition: number }` |
| `GET` | `/api/queue/status` | Get queue status | None | `{ success: boolean, data: QueueStatus }` |
| `GET` | `/api/queue/detailed` | Get detailed queue info (admin) | Admin auth required | `{ success: boolean, data: DetailedQueueStatus }` |

---

## 🔐 Secrets Management Routes (`/api/secrets`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/secrets` | Store new secret (admin) | `{ keyName: string, value: string, keyType: 'openai'|'anthropic'|'github'|'slack'|'stripe' }` | `{ success: boolean, data: SecretInfo }` |
| `GET` | `/api/secrets` | List all secrets (admin) | None | `{ success: boolean, data: Secret[] }` |
| `GET` | `/api/secrets/:keyName` | Get specific secret (admin) | `keyName: string` | `{ success: boolean, data: SecretData }` |
| `PUT` | `/api/secrets/:keyName` | Update secret (admin) | `{ keyName: string, value: string }` | `{ success: boolean, data: UpdatedSecret }` |
| `DELETE` | `/api/secrets/:keyName` | Delete secret (admin) | `keyName: string` | `{ success: boolean, message: string }` |
| `POST` | `/api/secrets/rotate` | Trigger secret rotation (admin) | Rotation config | `{ success: boolean, data: RotationResult }` |
| `POST` | `/api/secrets/initialize` | Initialize from env vars (admin) | None | `{ success: boolean, message: string }` |

---

## 📊 Logging Routes (`/api/logs`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/logs` | Create log entry | `{ level: 'error'|'warn'|'info'|'debug', message: string, metadata?: object, service: string, user_id?: UUID, session_id?: UUID }` | `{ success: boolean, data: LogEntry }` |
| `GET` | `/api/logs` | Get recent logs | `?service=string&level=string&limit=number` | `{ success: boolean, data: LogEntry[], count: number }` |
| `GET` | `/api/logs/metrics` | Get log metrics | `?timeRange=1h|6h|24h|7d|30d` | `{ success: boolean, data: LogMetrics }` |
| `GET` | `/api/logs/search` | Search logs | `?query=string&service=string&level=string&start_date=ISO&end_date=ISO&limit=number` | `{ success: boolean, data: LogEntry[] }` |

---

## 💳 Billing Routes (`/api/billing`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/billing/subscription` | Create subscription | `{ price_id: string, trial_days?: number }` | `{ success: boolean, data: SubscriptionData }` |
| `GET` | `/api/billing/subscription` | Get current subscription | None | `{ success: boolean, data: SubscriptionInfo }` |
| `POST` | `/api/billing/subscription/cancel` | Cancel subscription | None | `{ success: boolean, message: string }` |
| `POST` | `/api/billing/record-usage` | Record usage (internal) | `{ usage_type: string, quantity: number, session_id?: UUID, metadata?: object }` | `{ success: boolean, message: string }` |
| `GET` | `/api/billing/usage` | Get usage data | `?start_date=ISO&end_date=ISO` | `{ success: boolean, data: UsageData }` |
| `GET` | `/api/billing/plans` | Get subscription plans | None | `{ success: boolean, data: Plan[] }` |
| `POST` | `/api/billing/webhook` | Stripe webhook handler | Stripe webhook payload | `{ received: boolean }` |
| `GET` | `/api/billing/health` | Billing service health | None | `{ success: boolean, status: string, checks: object }` |

---

## 🚀 Onboarding Routes (`/api/onboarding`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `GET` | `/api/onboarding/progress` | Get onboarding progress | Auth required | `{ success: boolean, progress: OnboardingProgress }` |
| `POST` | `/api/onboarding/progress` | Update onboarding step | `{ step: 'api_keys'|'github_connection'|'sample_loop', completed?: boolean }` | `{ success: boolean, progress: OnboardingProgress }` |
| `POST` | `/api/onboarding/api-keys` | Save API keys | `{ openai?: string, anthropic?: string, github?: string }` | `{ success: boolean, results: SaveResult[], savedCount: number }` |
| `GET` | `/api/onboarding/status` | Check onboarding status | Auth required | `{ success: boolean, needsOnboarding: boolean, currentStep: number }` |
| `POST` | `/api/onboarding/skip` | Skip onboarding | Auth required | `{ success: boolean, message: string }` |
| `POST` | `/api/onboarding/reset` | Reset onboarding (dev) | Auth required | `{ success: boolean, message: string }` |

---

## 📋 Version Routes (`/api/version`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `GET` | `/api/version` | Get version info | None | `{ success: boolean, data: VersionInfo, timestamp: ISO }` |
| `GET` | `/api/version/changelog` | Get changelog | `?from=version&to=version` | `{ success: boolean, data: ChangelogData }` |
| `POST` | `/api/version/update-notification` | Record update notification | `{ version: string, action: 'shown'|'dismissed'|'updated' }` | `{ success: boolean, message: string }` |
| `GET` | `/api/version/health` | Version service health | None | `{ success: boolean, status: string, checks: object }` |

---

## 🔐 Authentication Routes (`/api/auth`)

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `POST` | `/api/auth/github/callback` | GitHub OAuth callback | `{ code: string }` | `{ access_token: string, token_type: string, scope: string }` |

---

## 🏥 Health & System Routes

| Method | Endpoint | Description | Request Schema | Response |
|--------|----------|-------------|----------------|----------|
| `GET` | `/health` | Basic health check | None | `{ status: 'ok', timestamp: ISO }` |

---

## 🌐 WebSocket Endpoints

| Protocol | Endpoint | Description | Message Format |
|----------|----------|-------------|----------------|
| `WebSocket` | `/` | Real-time streaming | `{ type: string, data: any, timestamp: ISO }` |

---

## 📝 Route Summary

**Total Endpoints**: 35
- **Reactor Loop**: 3 endpoints
- **Queue Management**: 3 endpoints  
- **Secrets Management**: 7 endpoints
- **Logging**: 4 endpoints
- **Billing**: 8 endpoints
- **Onboarding**: 6 endpoints
- **Version**: 4 endpoints
- **Authentication**: 1 endpoint
- **Health**: 1 endpoint
- **WebSocket**: 1 endpoint

**Authentication Requirements**:
- **Admin Only**: 7 endpoints (secrets management)
- **User Auth**: 6 endpoints (onboarding)
- **Public**: 22 endpoints

**Rate Limiting**:
- **Strict**: Secrets endpoints (10 req/min)
- **General**: All other endpoints (60 req/min)
