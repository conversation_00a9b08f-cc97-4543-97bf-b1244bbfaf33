
import { useEffect, useCallback } from 'react';

interface KeyboardShortcuts {
  onRunTransformation: () => void;
  onStopTransformation: () => void;
  onClearLogs: () => void;
  onApplyChanges: () => void;
}

export const useKeyboardShortcuts = ({
  onRunTransformation,
  onStopTransformation,
  onClearLogs,
  onApplyChanges
}: KeyboardShortcuts) => {
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Only trigger shortcuts when not in input fields
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement ||
        (event.target as HTMLElement)?.contentEditable === 'true') {
      return;
    }

    const isCtrlOrCmd = event.ctrlKey || event.metaKey;

    switch (true) {
      case isCtrlOrCmd && event.key === 'Enter':
        event.preventDefault();
        onRunTransformation();
        break;
      case event.key === 'Escape':
        event.preventDefault();
        onStopTransformation();
        break;
      case isCtrlOrCmd && event.shiftKey && event.key === 'K':
        event.preventDefault();
        onClearLogs();
        break;
      case isCtrlOrCmd && event.shiftKey && event.key === 'A':
        event.preventDefault();
        onApplyChanges();
        break;
    }
  }, [onRunTransformation, onStopTransformation, onClearLogs, onApplyChanges]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
};
