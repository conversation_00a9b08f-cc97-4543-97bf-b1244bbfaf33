import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from './ui/alert';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Separator } from './ui/separator';
import { RefreshCw, Download, X, Info, AlertTriangle } from 'lucide-react';
import { useToast } from './ui/use-toast';

interface VersionInfo {
  version: string;
  buildDate: string;
  gitCommit?: string;
  gitBranch?: string;
  environment: string;
  features: string[];
  apiVersion: string;
}

interface ChangelogEntry {
  date: string;
  changes: string[];
  breaking: string[];
  fixes: string[];
}

interface UpdateNotificationProps {
  checkInterval?: number; // in milliseconds, default 5 minutes
  currentVersion?: string;
}

export const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  checkInterval = 5 * 60 * 1000, // 5 minutes
  currentVersion = '1.0.0'
}) => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [latestVersion, setLatestVersion] = useState<VersionInfo | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [changelog, setChangelog] = useState<ChangelogEntry | null>(null);
  const [dismissed, setDismissed] = useState(false);
  const [checking, setChecking] = useState(false);
  const { toast } = useToast();

  // Check for updates
  const checkForUpdates = async (silent = false) => {
    if (!silent) setChecking(true);
    
    try {
      const response = await fetch(`/api/version/check?current=${currentVersion}`);
      const data = await response.json();
      
      if (data.success && data.updateAvailable) {
        setUpdateAvailable(true);
        setLatestVersion(data.data);
        
        if (!dismissed) {
          setShowBanner(true);
        }
        
        // Record that update notification was shown
        await fetch('/api/version/update-notification', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            version: data.data.version,
            action: 'shown'
          })
        });
        
        if (!silent) {
          toast({
            title: "Update Available",
            description: `Version ${data.data.version} is now available!`
          });
        }
      } else {
        setUpdateAvailable(false);
        setShowBanner(false);
      }
    } catch (error) {
      console.error('Failed to check for updates:', error);
      if (!silent) {
        toast({
          title: "Update Check Failed",
          description: "Could not check for updates. Please try again later.",
          variant: "destructive"
        });
      }
    } finally {
      if (!silent) setChecking(false);
    }
  };

  // Load changelog for the new version
  const loadChangelog = async (version: string) => {
    try {
      const response = await fetch(`/api/version/changelog?to=${version}`);
      const data = await response.json();
      
      if (data.success) {
        setChangelog(data.changelog);
      }
    } catch (error) {
      console.error('Failed to load changelog:', error);
    }
  };

  // Handle update action
  const handleUpdate = async () => {
    // Record user action
    await fetch('/api/version/update-notification', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        version: latestVersion?.version,
        action: 'updated'
      })
    });
    
    // Reload the page to get the new version
    window.location.reload();
  };

  // Handle dismiss
  const handleDismiss = async () => {
    setDismissed(true);
    setShowBanner(false);
    
    // Record dismissal
    await fetch('/api/version/update-notification', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        version: latestVersion?.version,
        action: 'dismissed'
      })
    });
  };

  // Show update details
  const showUpdateDetails = async () => {
    if (latestVersion) {
      await loadChangelog(latestVersion.version);
      setShowDetails(true);
    }
  };

  // Set up periodic update checking
  useEffect(() => {
    // Initial check
    checkForUpdates(true);
    
    // Set up interval for periodic checks
    const interval = setInterval(() => {
      checkForUpdates(true);
    }, checkInterval);
    
    return () => clearInterval(interval);
  }, [checkInterval, currentVersion]);

  // Update banner component
  const UpdateBanner = () => {
    if (!showBanner || !updateAvailable || !latestVersion) return null;

    return (
      <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
        <Download className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span>
              A new version ({latestVersion.version}) is available!
            </span>
            <Badge variant="secondary">
              {currentVersion} → {latestVersion.version}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={showUpdateDetails}
            >
              <Info className="w-3 h-3 mr-1" />
              Details
            </Button>
            <Button
              size="sm"
              onClick={handleUpdate}
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh for v{latestVersion.version}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  };

  // Update details dialog
  const UpdateDetailsDialog = () => {
    if (!showDetails || !latestVersion) return null;

    return (
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Download className="w-5 h-5" />
              <span>Update Available</span>
              <Badge variant="secondary">
                v{latestVersion.version}
              </Badge>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Version Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Version Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Current Version:</span>
                    <span className="ml-2">{currentVersion}</span>
                  </div>
                  <div>
                    <span className="font-medium">New Version:</span>
                    <span className="ml-2">{latestVersion.version}</span>
                  </div>
                  <div>
                    <span className="font-medium">Build Date:</span>
                    <span className="ml-2">
                      {new Date(latestVersion.buildDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Environment:</span>
                    <span className="ml-2 capitalize">{latestVersion.environment}</span>
                  </div>
                  {latestVersion.gitCommit && (
                    <div>
                      <span className="font-medium">Git Commit:</span>
                      <span className="ml-2 font-mono">{latestVersion.gitCommit}</span>
                    </div>
                  )}
                  {latestVersion.gitBranch && (
                    <div>
                      <span className="font-medium">Branch:</span>
                      <span className="ml-2">{latestVersion.gitBranch}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Changelog */}
            {changelog && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">What's New</CardTitle>
                  <CardDescription>
                    Changes in version {latestVersion.version}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {changelog.changes.length > 0 && (
                    <div>
                      <h4 className="font-medium text-green-600 mb-2">✨ New Features & Improvements</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {changelog.changes.map((change, index) => (
                          <li key={index}>{change}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {changelog.fixes.length > 0 && (
                    <div>
                      <h4 className="font-medium text-blue-600 mb-2">🐛 Bug Fixes</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {changelog.fixes.map((fix, index) => (
                          <li key={index}>{fix}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {changelog.breaking.length > 0 && (
                    <div>
                      <h4 className="font-medium text-red-600 mb-2 flex items-center">
                        <AlertTriangle className="w-4 h-4 mr-1" />
                        Breaking Changes
                      </h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {changelog.breaking.map((breaking, index) => (
                          <li key={index}>{breaking}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Features */}
            {latestVersion.features.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Enabled Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {latestVersion.features.map((feature) => (
                      <Badge key={feature} variant="outline">
                        {feature.replace('-', ' ')}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <Separator />

            {/* Actions */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setShowDetails(false)}>
                Maybe Later
              </Button>
              <Button onClick={handleUpdate}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Update Now
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Manual check button (for development/testing)
  const ManualCheckButton = () => {
    if (process.env.NODE_ENV !== 'development') return null;

    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => checkForUpdates(false)}
        disabled={checking}
        className="fixed bottom-4 right-4 z-50"
      >
        {checking ? (
          <RefreshCw className="w-4 h-4 animate-spin" />
        ) : (
          <RefreshCw className="w-4 h-4" />
        )}
        Check Updates
      </Button>
    );
  };

  return (
    <>
      <UpdateBanner />
      <UpdateDetailsDialog />
      <ManualCheckButton />
    </>
  );
};
