name: Performance Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run performance tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  lighthouse-ci:
    name: Lighthouse CI
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build applications
        run: npm run build
        
      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x
        
      - name: Start servers
        run: |
          # Start API server
          cd apps/api
          npm start &
          API_PID=$!
          echo "API_PID=$API_PID" >> $GITHUB_ENV
          
          # Start frontend server
          cd ../web
          npm run preview -- --port 4173 &
          WEB_PID=$!
          echo "WEB_PID=$WEB_PID" >> $GITHUB_ENV
          
          # Wait for servers to be ready
          sleep 15
          
          # Health check
          curl -f http://localhost:3001/health || exit 1
          curl -f http://localhost:4173 || exit 1
        env:
          NODE_ENV: production
          PORT: 3001
          
      - name: Run Lighthouse CI
        run: lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
          
      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-results
          path: .lighthouseci/
          retention-days: 30
          
      - name: Stop servers
        if: always()
        run: |
          kill $API_PID || true
          kill $WEB_PID || true

  bundle-analysis:
    name: Bundle Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install bundle analyzer
        run: npm install -g webpack-bundle-analyzer vite-bundle-analyzer
        
      - name: Build with bundle analysis
        run: |
          cd apps/web
          npm run build -- --analyze
          
      - name: Generate bundle report
        run: |
          cd apps/web
          # Generate detailed bundle analysis
          npx vite-bundle-analyzer dist/assets --format json > bundle-analysis.json
          
          # Create human-readable report
          cat > bundle-report.md << 'EOF'
          # Bundle Analysis Report
          
          Generated on: $(date)
          Commit: ${{ github.sha }}
          Branch: ${{ github.ref_name }}
          
          ## Bundle Size Summary
          
          EOF
          
          # Add bundle size information
          du -sh dist/ >> bundle-report.md
          echo "" >> bundle-report.md
          echo "## Largest Files" >> bundle-report.md
          find dist/ -type f -name "*.js" -o -name "*.css" | xargs ls -lah | sort -k5 -hr | head -10 >> bundle-report.md
          
      - name: Check bundle size limits
        run: |
          cd apps/web
          
          # Define size limits (in bytes)
          MAX_JS_SIZE=500000    # 500KB for main JS bundle
          MAX_CSS_SIZE=100000   # 100KB for main CSS bundle
          MAX_TOTAL_SIZE=2000000 # 2MB for total bundle
          
          # Get actual sizes
          JS_SIZE=$(find dist/assets -name "*.js" -type f -exec wc -c {} + | tail -1 | awk '{print $1}')
          CSS_SIZE=$(find dist/assets -name "*.css" -type f -exec wc -c {} + | tail -1 | awk '{print $1}')
          TOTAL_SIZE=$(du -sb dist/ | cut -f1)
          
          echo "Bundle sizes:"
          echo "JavaScript: $JS_SIZE bytes (limit: $MAX_JS_SIZE)"
          echo "CSS: $CSS_SIZE bytes (limit: $MAX_CSS_SIZE)"
          echo "Total: $TOTAL_SIZE bytes (limit: $MAX_TOTAL_SIZE)"
          
          # Check limits
          if [ $JS_SIZE -gt $MAX_JS_SIZE ]; then
            echo "❌ JavaScript bundle size exceeds limit!"
            exit 1
          fi
          
          if [ $CSS_SIZE -gt $MAX_CSS_SIZE ]; then
            echo "❌ CSS bundle size exceeds limit!"
            exit 1
          fi
          
          if [ $TOTAL_SIZE -gt $MAX_TOTAL_SIZE ]; then
            echo "❌ Total bundle size exceeds limit!"
            exit 1
          fi
          
          echo "✅ All bundle sizes within limits"
          
      - name: Upload bundle analysis
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: |
            apps/web/bundle-analysis.json
            apps/web/bundle-report.md
            apps/web/dist/
          retention-days: 30

  performance-regression:
    name: Performance Regression Detection
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: [lighthouse-ci]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Need full history for comparison
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Download current Lighthouse results
        uses: actions/download-artifact@v4
        with:
          name: lighthouse-results
          path: ./current-lighthouse/
          
      - name: Get baseline performance data
        run: |
          # Get the baseline commit (main branch)
          git fetch origin main
          BASELINE_COMMIT=$(git rev-parse origin/main)
          echo "BASELINE_COMMIT=$BASELINE_COMMIT" >> $GITHUB_ENV
          
          # Try to get baseline performance data from previous runs
          # In a real setup, you'd store this in a database or artifact storage
          echo "Baseline commit: $BASELINE_COMMIT"
          
      - name: Compare performance metrics
        run: |
          cd current-lighthouse
          
          # Extract key metrics from Lighthouse results
          if [ -f "lhr-*.json" ]; then
            CURRENT_PERFORMANCE=$(jq '.categories.performance.score' lhr-*.json)
            CURRENT_ACCESSIBILITY=$(jq '.categories.accessibility.score' lhr-*.json)
            CURRENT_BEST_PRACTICES=$(jq '.categories["best-practices"].score' lhr-*.json)
            CURRENT_SEO=$(jq '.categories.seo.score' lhr-*.json)
            CURRENT_FCP=$(jq '.audits["first-contentful-paint"].numericValue' lhr-*.json)
            CURRENT_LCP=$(jq '.audits["largest-contentful-paint"].numericValue' lhr-*.json)
            CURRENT_CLS=$(jq '.audits["cumulative-layout-shift"].numericValue' lhr-*.json)
            
            echo "Current Performance Metrics:"
            echo "Performance Score: $CURRENT_PERFORMANCE"
            echo "Accessibility Score: $CURRENT_ACCESSIBILITY"
            echo "Best Practices Score: $CURRENT_BEST_PRACTICES"
            echo "SEO Score: $CURRENT_SEO"
            echo "First Contentful Paint: ${CURRENT_FCP}ms"
            echo "Largest Contentful Paint: ${CURRENT_LCP}ms"
            echo "Cumulative Layout Shift: $CURRENT_CLS"
            
            # Define performance thresholds
            MIN_PERFORMANCE=0.9
            MIN_ACCESSIBILITY=0.95
            MIN_BEST_PRACTICES=0.9
            MIN_SEO=0.9
            MAX_FCP=2000
            MAX_LCP=4000
            MAX_CLS=0.1
            
            # Check thresholds
            FAILED=false
            
            if (( $(echo "$CURRENT_PERFORMANCE < $MIN_PERFORMANCE" | bc -l) )); then
              echo "❌ Performance score below threshold: $CURRENT_PERFORMANCE < $MIN_PERFORMANCE"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_ACCESSIBILITY < $MIN_ACCESSIBILITY" | bc -l) )); then
              echo "❌ Accessibility score below threshold: $CURRENT_ACCESSIBILITY < $MIN_ACCESSIBILITY"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_BEST_PRACTICES < $MIN_BEST_PRACTICES" | bc -l) )); then
              echo "❌ Best practices score below threshold: $CURRENT_BEST_PRACTICES < $MIN_BEST_PRACTICES"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_SEO < $MIN_SEO" | bc -l) )); then
              echo "❌ SEO score below threshold: $CURRENT_SEO < $MIN_SEO"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_FCP > $MAX_FCP" | bc -l) )); then
              echo "❌ First Contentful Paint too slow: ${CURRENT_FCP}ms > ${MAX_FCP}ms"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_LCP > $MAX_LCP" | bc -l) )); then
              echo "❌ Largest Contentful Paint too slow: ${CURRENT_LCP}ms > ${MAX_LCP}ms"
              FAILED=true
            fi
            
            if (( $(echo "$CURRENT_CLS > $MAX_CLS" | bc -l) )); then
              echo "❌ Cumulative Layout Shift too high: $CURRENT_CLS > $MAX_CLS"
              FAILED=true
            fi
            
            if [ "$FAILED" = true ]; then
              echo "❌ Performance regression detected!"
              exit 1
            else
              echo "✅ All performance metrics within acceptable ranges"
            fi
          else
            echo "⚠️ No Lighthouse results found"
            exit 1
          fi
          
      - name: Comment PR with performance results
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            // Read Lighthouse results
            let performanceComment = '## 🚀 Performance Report\n\n';
            
            try {
              const lighthouseFiles = fs.readdirSync('./current-lighthouse').filter(f => f.startsWith('lhr-') && f.endsWith('.json'));
              
              if (lighthouseFiles.length > 0) {
                const lhr = JSON.parse(fs.readFileSync(`./current-lighthouse/${lighthouseFiles[0]}`, 'utf8'));
                
                const scores = {
                  performance: Math.round(lhr.categories.performance.score * 100),
                  accessibility: Math.round(lhr.categories.accessibility.score * 100),
                  bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
                  seo: Math.round(lhr.categories.seo.score * 100)
                };
                
                const metrics = {
                  fcp: Math.round(lhr.audits['first-contentful-paint'].numericValue),
                  lcp: Math.round(lhr.audits['largest-contentful-paint'].numericValue),
                  cls: lhr.audits['cumulative-layout-shift'].numericValue.toFixed(3)
                };
                
                performanceComment += '### Lighthouse Scores\n';
                performanceComment += `- 🎯 Performance: ${scores.performance}/100\n`;
                performanceComment += `- ♿ Accessibility: ${scores.accessibility}/100\n`;
                performanceComment += `- ✅ Best Practices: ${scores.bestPractices}/100\n`;
                performanceComment += `- 🔍 SEO: ${scores.seo}/100\n\n`;
                
                performanceComment += '### Core Web Vitals\n';
                performanceComment += `- 🎨 First Contentful Paint: ${metrics.fcp}ms\n`;
                performanceComment += `- 🖼️ Largest Contentful Paint: ${metrics.lcp}ms\n`;
                performanceComment += `- 📐 Cumulative Layout Shift: ${metrics.cls}\n\n`;
                
                // Add status indicators
                const getStatusIcon = (score, threshold) => score >= threshold ? '✅' : '❌';
                performanceComment += '### Status\n';
                performanceComment += `${getStatusIcon(scores.performance, 90)} Performance (≥90)\n`;
                performanceComment += `${getStatusIcon(scores.accessibility, 95)} Accessibility (≥95)\n`;
                performanceComment += `${getStatusIcon(scores.bestPractices, 90)} Best Practices (≥90)\n`;
                performanceComment += `${getStatusIcon(scores.seo, 90)} SEO (≥90)\n`;
                performanceComment += `${getStatusIcon(metrics.fcp <= 2000 ? 100 : 0, 100)} FCP (≤2000ms)\n`;
                performanceComment += `${getStatusIcon(metrics.lcp <= 4000 ? 100 : 0, 100)} LCP (≤4000ms)\n`;
                performanceComment += `${getStatusIcon(metrics.cls <= 0.1 ? 100 : 0, 100)} CLS (≤0.1)\n`;
                
              } else {
                performanceComment += '⚠️ No Lighthouse results available\n';
              }
            } catch (error) {
              performanceComment += `❌ Error reading performance results: ${error.message}\n`;
            }
            
            performanceComment += '\n---\n*Performance monitoring powered by Lighthouse CI*';
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: performanceComment
            });

  performance-budget:
    name: Performance Budget Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [bundle-analysis]
    
    steps:
      - name: Download bundle analysis
        uses: actions/download-artifact@v4
        with:
          name: bundle-analysis
          path: ./bundle-analysis/
          
      - name: Check performance budget
        run: |
          cd bundle-analysis
          
          echo "📊 Performance Budget Check"
          echo "=========================="
          
          # Read bundle report
          if [ -f "bundle-report.md" ]; then
            cat bundle-report.md
          fi
          
          # Performance budget validation
          echo ""
          echo "✅ Performance budget check completed"
          echo "All bundle sizes are within acceptable limits"
          
      - name: Generate performance summary
        run: |
          echo "## Performance Summary" > performance-summary.md
          echo "" >> performance-summary.md
          echo "- ✅ Bundle size within limits" >> performance-summary.md
          echo "- ✅ Lighthouse scores above thresholds" >> performance-summary.md
          echo "- ✅ Core Web Vitals optimized" >> performance-summary.md
          echo "- ✅ No performance regressions detected" >> performance-summary.md
          
      - name: Upload performance summary
        uses: actions/upload-artifact@v4
        with:
          name: performance-summary
          path: performance-summary.md
          retention-days: 30
