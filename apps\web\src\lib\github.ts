import { supabase } from './supabase';

export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  owner: {
    login: string;
  };
  default_branch: string;
  permissions: {
    admin: boolean;
    push: boolean;
    pull: boolean;
  };
}

export interface CreatePRRequest {
  owner: string;
  repo: string;
  title: string;
  body: string;
  head: string;
  base: string;
  patch: any;
  originalContent?: string;
}

export interface CreatePRResponse {
  url: string;
  number: number;
  html_url: string;
}

class GitHubService {
  private baseUrl = 'https://api.github.com';

  async getStoredToken(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data, error } = await supabase
        .from('github_tokens')
        .select('access_token_encrypted')
        .eq('user_id', user.id)
        .single();

      if (error || !data?.access_token_encrypted) return null;

      // Decrypt the token
      const { data: decryptedToken } = await supabase.rpc('decrypt_secret', {
        encrypted_secret: data.access_token_encrypted
      });

      return decryptedToken;
    } catch (error) {
      console.error('Error getting stored GitHub token:', error);
      return null;
    }
  }

  async storeToken(accessToken: string, userInfo: GitHubUser): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Encrypt the token
      const { data: encryptedToken } = await supabase.rpc('encrypt_secret', {
        secret: accessToken
      });

      if (!encryptedToken) throw new Error('Failed to encrypt token');

      const { error } = await supabase
        .from('github_tokens')
        .upsert({
          user_id: user.id,
          access_token_encrypted: encryptedToken,
          github_user_id: userInfo.id,
          github_username: userInfo.login,
          scopes: ['repo', 'user:email'],
          updated_at: new Date().toISOString(),
        }, { onConflict: 'user_id' });

      if (error) throw error;
    } catch (error) {
      console.error('Error storing GitHub token:', error);
      throw error;
    }
  }

  async removeToken(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('github_tokens')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Error removing GitHub token:', error);
      throw error;
    }
  }

  async makeAuthenticatedRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = await this.getStoredToken();
    if (!token) {
      throw new Error('No GitHub token available. Please connect your GitHub account.');
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`GitHub API error: ${response.status} ${response.statusText} - ${errorData.message || 'Unknown error'}`);
    }

    return response.json();
  }

  async getCurrentUser(): Promise<GitHubUser> {
    return this.makeAuthenticatedRequest('/user');
  }

  async getUserRepos(): Promise<GitHubRepo[]> {
    const repos = await this.makeAuthenticatedRequest('/user/repos?sort=updated&per_page=100');
    return repos.filter((repo: any) => repo.permissions.push);
  }

  async createBranch(owner: string, repo: string, branchName: string, baseBranch: string = 'main'): Promise<void> {
    // Get the SHA of the base branch
    const baseRef = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/ref/heads/${baseBranch}`);
    const baseSha = baseRef.object.sha;

    // Create new branch
    await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/refs`, {
      method: 'POST',
      body: JSON.stringify({
        ref: `refs/heads/${branchName}`,
        sha: baseSha,
      }),
    });
  }

  async createCommit(
    owner: string, 
    repo: string, 
    branchName: string, 
    message: string, 
    files: Array<{ path: string; content: string }>
  ): Promise<void> {
    // Get the current commit SHA
    const branchRef = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/ref/heads/${branchName}`);
    const currentCommitSha = branchRef.object.sha;

    // Get the current tree
    const currentCommit = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/commits/${currentCommitSha}`);
    const currentTreeSha = currentCommit.tree.sha;

    // Create blobs for each file
    const tree = await Promise.all(
      files.map(async (file) => {
        const blob = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/blobs`, {
          method: 'POST',
          body: JSON.stringify({
            content: file.content,
            encoding: 'utf-8',
          }),
        });

        return {
          path: file.path,
          mode: '100644',
          type: 'blob',
          sha: blob.sha,
        };
      })
    );

    // Create new tree
    const newTree = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/trees`, {
      method: 'POST',
      body: JSON.stringify({
        base_tree: currentTreeSha,
        tree,
      }),
    });

    // Create new commit
    const newCommit = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/commits`, {
      method: 'POST',
      body: JSON.stringify({
        message,
        tree: newTree.sha,
        parents: [currentCommitSha],
      }),
    });

    // Update branch reference
    await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/git/refs/heads/${branchName}`, {
      method: 'PATCH',
      body: JSON.stringify({
        sha: newCommit.sha,
      }),
    });
  }

  async createPullRequest(request: CreatePRRequest): Promise<CreatePRResponse> {
    const { owner, repo, title, body, head, base } = request;

    const pr = await this.makeAuthenticatedRequest(`/repos/${owner}/${repo}/pulls`, {
      method: 'POST',
      body: JSON.stringify({
        title,
        body,
        head,
        base,
        draft: true, // Create as draft initially
      }),
    });

    return {
      url: pr.url,
      number: pr.number,
      html_url: pr.html_url,
    };
  }

  async createPRFromPatch(
    owner: string,
    repo: string,
    patch: any,
    originalContent: string = '',
    options: {
      title?: string;
      description?: string;
      baseBranch?: string;
    } = {}
  ): Promise<CreatePRResponse> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const branchName = `reactor/${timestamp}`;
    const baseBranch = options.baseBranch || 'main';

    // Create branch
    await this.createBranch(owner, repo, branchName, baseBranch);

    // Apply patch to create modified content
    let modifiedContent = originalContent;
    if (patch?.operations) {
      try {
        // Simple patch application - in a real implementation, this would be more sophisticated
        modifiedContent = this.applyPatchToContent(originalContent, patch);
      } catch (error) {
        console.warn('Failed to apply patch, using original content:', error);
      }
    }

    // Create commit with modified files
    const files = [
      {
        path: 'transformed-code.js', // This should be determined from the patch context
        content: modifiedContent,
      },
    ];

    const commitMessage = `feat: ${options.title || patch.description || 'Apply code transformation'}

Applied transformation with confidence: ${patch.confidence ? (patch.confidence * 100).toFixed(1) + '%' : 'N/A'}

Operations:
${patch.operations?.map((op: any, i: number) => `${i + 1}. ${op.op.toUpperCase()} ${op.path}`).join('\n') || 'No operations'}`;

    await this.createCommit(owner, repo, branchName, commitMessage, files);

    // Create pull request
    const prTitle = options.title || `🤖 Code Transformation: ${patch.description || 'Automated changes'}`;
    const prBody = `## 🔮 Metamorphic Reactor Transformation

**Description:** ${options.description || patch.description || 'Automated code transformation'}

**Confidence Score:** ${patch.confidence ? (patch.confidence * 100).toFixed(1) + '%' : 'N/A'}

### 📋 Applied Operations

${patch.operations?.map((op: any, i: number) => {
  return `${i + 1}. **${op.op.toUpperCase()}** \`${op.path}\`${op.value ? `\n   \`\`\`json\n   ${JSON.stringify(op.value, null, 2)}\n   \`\`\`` : ''}`;
}).join('\n\n') || 'No operations applied'}

### 🔍 Review Notes

- This PR was automatically generated by Metamorphic Reactor
- Please review the changes carefully before merging
- The transformation was applied with AI assistance

---
*Generated by [Metamorphic Reactor](https://github.com/your-org/metamorphic-reactor)*`;

    return this.createPullRequest({
      owner,
      repo,
      title: prTitle,
      body: prBody,
      head: branchName,
      base: baseBranch,
      patch,
      originalContent,
    });
  }

  private applyPatchToContent(content: string, patch: any): string {
    // This is a simplified patch application
    // In a real implementation, you'd use a proper JSON Patch library
    let result = content;

    if (patch.operations) {
      for (const op of patch.operations) {
        switch (op.op) {
          case 'add':
            if (op.path === '/end') {
              result += `\n${op.value}`;
            }
            break;
          case 'replace':
            if (op.path === '/content') {
              result = op.value;
            }
            break;
          // Add more operation types as needed
        }
      }
    }

    return result;
  }

  // OAuth flow helpers
  getOAuthUrl(): string {
    const clientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
    const redirectUri = `${window.location.origin}/auth/github/callback`;
    const scopes = 'repo,user:email';
    const state = Math.random().toString(36).substring(2, 15);
    
    // Store state for verification
    sessionStorage.setItem('github_oauth_state', state);

    return `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&state=${state}`;
  }

  async handleOAuthCallback(code: string, state: string): Promise<GitHubUser> {
    // Verify state
    const storedState = sessionStorage.getItem('github_oauth_state');
    if (state !== storedState) {
      throw new Error('Invalid OAuth state');
    }
    sessionStorage.removeItem('github_oauth_state');

    // Exchange code for access token via your backend
    const response = await fetch('/api/auth/github/callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code }),
    });

    if (!response.ok) {
      throw new Error('Failed to exchange code for token');
    }

    const { access_token } = await response.json();

    // Get user info
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Accept': 'application/vnd.github.v3+json',
      },
    });

    if (!userResponse.ok) {
      throw new Error('Failed to get user info');
    }

    const userInfo = await userResponse.json();

    // Store token
    await this.storeToken(access_token, userInfo);

    return userInfo;
  }
}

export const githubService = new GitHubService();
