import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface LoopRequest {
  prompt: string;
  maxIterations?: number;
  scoreThreshold?: number;
  context?: Record<string, any>;
}

interface JSONPatch {
  operations: Array<{
    op: string;
    path: string;
    value?: any;
  }>;
  description: string;
  confidence: number;
}

interface CritiqueResult {
  score: number;
  feedback: string;
  suggestions: string[];
  isAcceptable: boolean;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// AI API keys
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY');

// Cost and time guards
const MAX_EXECUTION_TIME = 180000; // 3 minutes
const MAX_COST_USD = 3.0;
const COST_PER_TOKEN_GPT4 = 0.00003; // $0.03 per 1K tokens
const COST_PER_TOKEN_CLAUDE = 0.000008; // $0.008 per 1K tokens

class AILoopService {
  private startTime: number;
  private totalCost: number = 0;

  constructor() {
    this.startTime = Date.now();
  }

  private checkGuards() {
    const elapsed = Date.now() - this.startTime;
    if (elapsed > MAX_EXECUTION_TIME) {
      throw new Error('Execution time limit exceeded (3 minutes)');
    }
    if (this.totalCost > MAX_COST_USD) {
      throw new Error('Cost limit exceeded ($3.00)');
    }
  }

  private estimateCost(tokens: number, model: 'gpt4' | 'claude'): number {
    const rate = model === 'gpt4' ? COST_PER_TOKEN_GPT4 : COST_PER_TOKEN_CLAUDE;
    return tokens * rate;
  }

  private stripSecrets(text: string): string {
    // Remove potential API keys, tokens, passwords
    return text
      .replace(/sk-[a-zA-Z0-9]{48}/g, '[REDACTED_API_KEY]')
      .replace(/ghp_[a-zA-Z0-9]{36}/g, '[REDACTED_GITHUB_TOKEN]')
      .replace(/password[:\s]*[^\s\n]+/gi, 'password: [REDACTED]')
      .replace(/token[:\s]*[^\s\n]+/gi, 'token: [REDACTED]');
  }

  async callGPT4(prompt: string): Promise<{ content: string; tokens: number }> {
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: this.stripSecrets(prompt) }],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    const tokens = data.usage?.total_tokens || 1000;
    this.totalCost += this.estimateCost(tokens, 'gpt4');

    return {
      content: data.choices[0]?.message?.content || '',
      tokens,
    };
  }

  async callClaude(prompt: string): Promise<{ content: string; tokens: number }> {
    if (!anthropicApiKey) {
      throw new Error('Anthropic API key not configured');
    }

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': anthropicApiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 1500,
        temperature: 0.3,
        messages: [{ role: 'user', content: this.stripSecrets(prompt) }],
      }),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`);
    }

    const data = await response.json();
    const tokens = data.usage?.input_tokens + data.usage?.output_tokens || 800;
    this.totalCost += this.estimateCost(tokens, 'claude');

    return {
      content: data.content[0]?.text || '',
      tokens,
    };
  }

  async generatePatch(prompt: string, previousAttempts: JSONPatch[] = []): Promise<JSONPatch> {
    this.checkGuards();

    const planningPrompt = `
You are a Plan Agent in a dual-agent code transformation system. Generate a JSON patch for the following request:

PROMPT: ${prompt}

${previousAttempts.length > 0 ? `
PREVIOUS ATTEMPTS:
${previousAttempts.map((p, i) => `${i + 1}. ${p.description} (confidence: ${p.confidence})`).join('\n')}

Learn from these previous attempts and generate an improved solution.
` : ''}

Return a valid JSON object with this exact structure:
{
  "operations": [
    {
      "op": "add|remove|replace|move|copy|test",
      "path": "/json/pointer/path",
      "value": "new value (if applicable)"
    }
  ],
  "description": "Clear description of what this patch does",
  "confidence": 0.85
}

Focus on:
1. Precise JSON Pointer paths
2. Valid RFC 6902 operations
3. Clear, actionable changes
4. Confidence between 0.0-1.0
`;

    try {
      const result = await this.callGPT4(planningPrompt);
      const patchData = JSON.parse(result.content);
      
      return {
        operations: patchData.operations || [],
        description: patchData.description || 'Generated patch',
        confidence: Math.min(Math.max(patchData.confidence || 0.8, 0), 1),
      };
    } catch (error) {
      console.error('GPT-4 failed, falling back to mock:', error);
      // Fallback to mock implementation
      return {
        operations: [
          {
            op: 'add',
            path: '/generated',
            value: { prompt, timestamp: new Date().toISOString() }
          }
        ],
        description: `Fallback patch for: ${prompt}`,
        confidence: 0.6,
      };
    }
  }

  async critiquePatch(patch: JSONPatch, originalPrompt: string): Promise<CritiqueResult> {
    this.checkGuards();

    const critiquePrompt = `
You are a Critique Agent in a dual-agent code transformation system. Evaluate this JSON patch:

ORIGINAL PROMPT: ${originalPrompt}

PATCH TO EVALUATE:
${JSON.stringify(patch, null, 2)}

Provide a detailed critique with this exact JSON structure:
{
  "score": 0.92,
  "feedback": "Detailed analysis of the patch quality, correctness, and alignment with the prompt",
  "suggestions": [
    "Specific suggestion 1",
    "Specific suggestion 2"
  ],
  "isAcceptable": true
}

Scoring criteria (0.0-1.0):
- 0.95+: Excellent, ready for production
- 0.85-0.94: Good, minor improvements needed
- 0.70-0.84: Acceptable, some issues to address
- Below 0.70: Needs significant improvement

Consider:
1. Correctness of JSON Pointer paths
2. Validity of operations
3. Alignment with original prompt
4. Potential side effects
5. Code quality and best practices
`;

    try {
      const result = await this.callClaude(critiquePrompt);
      const critiqueData = JSON.parse(result.content);
      
      return {
        score: Math.min(Math.max(critiqueData.score || 0.7, 0), 1),
        feedback: critiqueData.feedback || 'Generated critique',
        suggestions: critiqueData.suggestions || [],
        isAcceptable: critiqueData.score >= 0.95,
      };
    } catch (error) {
      console.error('Claude failed, using fallback critique:', error);
      // Fallback critique
      const mockScore = 0.7 + Math.random() * 0.2;
      return {
        score: mockScore,
        feedback: `Fallback critique: The patch appears to address the prompt "${originalPrompt}" with basic functionality.`,
        suggestions: ['Consider adding error handling', 'Validate input parameters'],
        isAcceptable: mockScore >= 0.95,
      };
    }
  }
}

Deno.serve(async (req: Request) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { prompt, maxIterations = 10, scoreThreshold = 0.95, context = {} }: LoopRequest = await req.json();

    if (!prompt?.trim()) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create session record
    const { data: session, error: sessionError } = await supabase
      .from('reactor_sessions')
      .insert({
        prompt,
        max_loops: maxIterations,
        status: 'running',
      })
      .select()
      .single();

    if (sessionError) {
      throw new Error(`Failed to create session: ${sessionError.message}`);
    }

    const aiService = new AILoopService();
    const logs: any[] = [];
    let currentPatch: JSONPatch | null = null;
    let finalScore = 0;
    let iteration = 0;
    const previousAttempts: JSONPatch[] = [];

    // Main loop
    while (iteration < maxIterations) {
      iteration++;
      
      try {
        // Generate patch
        const patch = await aiService.generatePatch(prompt, previousAttempts);
        currentPatch = patch;
        
        // Critique patch
        const critique = await aiService.critiquePatch(patch, prompt);
        finalScore = critique.score;
        
        // Log iteration
        const logEntry = {
          session_id: session.id,
          iteration,
          plan: patch.description,
          critique: critique.feedback,
          score: critique.score,
          patch,
        };
        
        logs.push(logEntry);
        
        // Save to database
        await supabase.from('agent_logs').insert(logEntry);
        
        // Check if acceptable
        if (critique.isAcceptable || critique.score >= scoreThreshold) {
          break;
        }
        
        // Add to previous attempts for next iteration
        previousAttempts.push(patch);
        
      } catch (error) {
        console.error(`Iteration ${iteration} failed:`, error);
        break;
      }
    }

    // Update session
    await supabase
      .from('reactor_sessions')
      .update({
        final_score: finalScore,
        final_patch: currentPatch,
        iterations_count: iteration,
        status: 'completed',
        completed_at: new Date().toISOString(),
      })
      .eq('id', session.id);

    return new Response(
      JSON.stringify({
        success: true,
        sessionId: session.id,
        finalPatch: currentPatch,
        finalScore,
        iterations: iteration,
        logs,
        completed: true,
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('AI Loop error:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        success: false 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
