import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const GitHubCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing GitHub authentication...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          throw new Error(`GitHub OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received from GitHub');
        }

        // Verify state parameter
        const storedState = localStorage.getItem('github_oauth_state');
        if (!storedState || storedState !== state) {
          throw new Error('Invalid state parameter - possible CSRF attack');
        }

        // Clean up stored state
        localStorage.removeItem('github_oauth_state');

        // Get current session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated with Supabase');
        }

        setMessage('Exchanging code for access token...');

        // Exchange code for access token via Edge Function
        const response = await fetch('/functions/v1/github-oauth', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code, state }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to exchange code for token');
        }

        const result = await response.json();
        
        setStatus('success');
        setMessage(`Successfully connected GitHub account: ${result.user.login}`);

        toast({
          title: "GitHub Connected!",
          description: `Your GitHub account (${result.user.login}) has been successfully connected.`,
        });

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);

      } catch (error) {
        console.error('GitHub OAuth callback error:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Unknown error occurred');

        toast({
          title: "GitHub Connection Failed",
          description: error instanceof Error ? error.message : 'Failed to connect GitHub account',
          variant: "destructive",
        });

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      }
    };

    handleCallback();
  }, [searchParams, navigate, toast]);

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="bg-slate-800 rounded-lg p-8 text-center">
          <div className="mb-6">
            {status === 'loading' && (
              <Loader2 className="w-12 h-12 mx-auto text-indigo-400 animate-spin" />
            )}
            {status === 'success' && (
              <CheckCircle className="w-12 h-12 mx-auto text-green-400" />
            )}
            {status === 'error' && (
              <XCircle className="w-12 h-12 mx-auto text-red-400" />
            )}
          </div>

          <h1 className="text-xl font-semibold text-white mb-4">
            {status === 'loading' && 'Connecting GitHub...'}
            {status === 'success' && 'GitHub Connected!'}
            {status === 'error' && 'Connection Failed'}
          </h1>

          <p className="text-slate-300 mb-6">
            {message}
          </p>

          {status !== 'loading' && (
            <p className="text-sm text-slate-400">
              Redirecting to dashboard...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default GitHubCallback;
