name: Nightly Database Backup

on:
  schedule:
    # Run at 2 AM UTC every day
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      force_backup:
        description: 'Force backup even if not scheduled'
        required: false
        default: 'false'
        type: boolean

env:
  SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
  SUPABASE_PROJECT_REF: ${{ secrets.SUPABASE_PROJECT_REF }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  BACKUP_S3_BUCKET: ${{ secrets.BACKUP_S3_BUCKET }}
  BACKUP_S3_REGION: ${{ secrets.BACKUP_S3_REGION }}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

jobs:
  backup:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          
      - name: Install AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.BACKUP_S3_REGION }}
          
      - name: Verify backup script permissions
        run: chmod +x scripts/backup.sh
        
      - name: Run database backup
        id: backup
        run: |
          echo "Starting nightly backup..."
          ./scripts/backup.sh backup
          echo "backup_status=success" >> $GITHUB_OUTPUT
        continue-on-error: true
        
      - name: Check backup status
        if: steps.backup.outcome == 'failure'
        run: |
          echo "backup_status=failed" >> $GITHUB_OUTPUT
          echo "Backup failed - check logs for details"
          exit 1
          
      - name: Send success notification
        if: steps.backup.outcome == 'success'
        run: |
          if [ -n "$SLACK_WEBHOOK_URL" ]; then
            curl -X POST "$SLACK_WEBHOOK_URL" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "✅ Nightly Database Backup Completed",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Nightly Backup Status*\n\n• *Status:* ✅ Success\n• *Project:* Metamorphic Reactor\n• *Time:* '"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'\n• *Workflow:* [View Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
                    }
                  }
                ]
              }'
          fi
          
      - name: Send failure notification
        if: steps.backup.outcome == 'failure'
        run: |
          if [ -n "$SLACK_WEBHOOK_URL" ]; then
            curl -X POST "$SLACK_WEBHOOK_URL" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "❌ Nightly Database Backup Failed",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Nightly Backup Status*\n\n• *Status:* ❌ Failed\n• *Project:* Metamorphic Reactor\n• *Time:* '"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'\n• *Workflow:* [View Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n• *Action Required:* Check backup logs and resolve issues"
                    }
                  }
                ]
              }'
          fi

  verify-backup:
    needs: backup
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: needs.backup.result == 'success'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Install AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.BACKUP_S3_REGION }}
          
      - name: Verify latest backup
        run: |
          echo "Verifying latest backup integrity..."
          
          # Get the latest backup file
          LATEST_BACKUP=$(aws s3 ls s3://$BACKUP_S3_BUCKET/backups/ --recursive | sort | tail -n 1 | awk '{print $4}')
          
          if [ -z "$LATEST_BACKUP" ]; then
            echo "No backup files found"
            exit 1
          fi
          
          echo "Latest backup: $LATEST_BACKUP"
          
          # Download and verify
          aws s3 cp "s3://$BACKUP_S3_BUCKET/$LATEST_BACKUP" /tmp/verify_backup.sql.gz
          
          # Test compression integrity
          if gzip -t /tmp/verify_backup.sql.gz; then
            echo "✅ Backup compression integrity verified"
          else
            echo "❌ Backup compression is corrupted"
            exit 1
          fi
          
          # Test SQL content
          if zcat /tmp/verify_backup.sql.gz | head -100 | grep -q "PostgreSQL database dump"; then
            echo "✅ Backup appears to be a valid PostgreSQL dump"
          else
            echo "⚠️ Backup may not be a valid PostgreSQL dump"
          fi
          
          # Get file size
          FILE_SIZE=$(stat -c%s /tmp/verify_backup.sql.gz)
          echo "Backup file size: $(numfmt --to=iec $FILE_SIZE)"
          
          # Clean up
          rm -f /tmp/verify_backup.sql.gz
          
          echo "Backup verification completed successfully"

  cleanup-old-backups:
    needs: [backup, verify-backup]
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: needs.backup.result == 'success'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Install AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.BACKUP_S3_REGION }}
          
      - name: Clean up old backups
        run: |
          echo "Cleaning up backups older than 30 days..."
          
          # Calculate cutoff date (30 days ago)
          CUTOFF_DATE=$(date -d "30 days ago" +%Y-%m-%d)
          echo "Cutoff date: $CUTOFF_DATE"
          
          # List and delete old backups
          DELETED_COUNT=0
          aws s3 ls s3://$BACKUP_S3_BUCKET/backups/ --recursive | while read -r line; do
            FILE_DATE=$(echo "$line" | awk '{print $1}')
            FILE_NAME=$(echo "$line" | awk '{print $4}')
            
            if [[ "$FILE_DATE" < "$CUTOFF_DATE" ]]; then
              echo "Deleting old backup: $FILE_NAME (date: $FILE_DATE)"
              aws s3 rm "s3://$BACKUP_S3_BUCKET/$FILE_NAME"
              DELETED_COUNT=$((DELETED_COUNT + 1))
            fi
          done
          
          echo "Cleanup completed. Deleted $DELETED_COUNT old backup files."

  health-check:
    needs: [backup, verify-backup, cleanup-old-backups]
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: always()
    
    steps:
      - name: Report backup health status
        run: |
          BACKUP_STATUS="${{ needs.backup.result }}"
          VERIFY_STATUS="${{ needs.verify-backup.result }}"
          CLEANUP_STATUS="${{ needs.cleanup-old-backups.result }}"
          
          echo "=== Nightly Backup Health Report ==="
          echo "Backup Job: $BACKUP_STATUS"
          echo "Verification Job: $VERIFY_STATUS"
          echo "Cleanup Job: $CLEANUP_STATUS"
          
          # Determine overall health
          if [[ "$BACKUP_STATUS" == "success" && "$VERIFY_STATUS" == "success" ]]; then
            echo "Overall Status: ✅ HEALTHY"
            OVERALL_STATUS="healthy"
          elif [[ "$BACKUP_STATUS" == "success" ]]; then
            echo "Overall Status: ⚠️ PARTIAL"
            OVERALL_STATUS="partial"
          else
            echo "Overall Status: ❌ UNHEALTHY"
            OVERALL_STATUS="unhealthy"
          fi
          
          # Send health report to Slack
          if [ -n "$SLACK_WEBHOOK_URL" ]; then
            EMOJI="✅"
            if [[ "$OVERALL_STATUS" == "partial" ]]; then
              EMOJI="⚠️"
            elif [[ "$OVERALL_STATUS" == "unhealthy" ]]; then
              EMOJI="❌"
            fi
            
            curl -X POST "$SLACK_WEBHOOK_URL" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "'"$EMOJI"' Backup Health Report",
                "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Nightly Backup Health Report*\n\n• *Backup:* '"$BACKUP_STATUS"'\n• *Verification:* '"$VERIFY_STATUS"'\n• *Cleanup:* '"$CLEANUP_STATUS"'\n• *Overall:* '"$OVERALL_STATUS"'\n• *Time:* '"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'"
                    }
                  }
                ]
              }'
          fi
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
