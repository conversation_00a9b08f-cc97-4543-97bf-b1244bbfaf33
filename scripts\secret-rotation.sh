#!/bin/bash

# Secret Rotation Script for Metamorphic Reactor
# This script checks for secrets that need rotation and triggers the rotation process

set -e

# Configuration
SUPABASE_URL="${SUPABASE_URL:-}"
SUPABASE_SERVICE_ROLE_KEY="${SUPABASE_SERVICE_ROLE_KEY:-}"
API_BASE_URL="${API_BASE_URL:-http://localhost:3001}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Check if required environment variables are set
check_env() {
    if [[ -z "$SUPABASE_URL" ]]; then
        error "SUPABASE_URL environment variable is not set"
        exit 1
    fi
    
    if [[ -z "$SUPABASE_SERVICE_ROLE_KEY" ]]; then
        error "SUPABASE_SERVICE_ROLE_KEY environment variable is not set"
        exit 1
    fi
    
    log "Environment variables validated"
}

# Check which secrets need rotation
check_rotation_needed() {
    log "Checking for secrets that need rotation..."
    
    local response=$(curl -s -X GET \
        "${API_BASE_URL}/api/secrets/rotation-check" \
        -H "Authorization: Bearer ${SUPABASE_SERVICE_ROLE_KEY}" \
        -H "Content-Type: application/json")
    
    if [[ $? -ne 0 ]]; then
        error "Failed to check secrets for rotation"
        return 1
    fi
    
    local count=$(echo "$response" | jq -r '.count // 0')
    
    if [[ "$count" -gt 0 ]]; then
        log "Found $count secrets that need rotation"
        echo "$response" | jq -r '.data[]'
        return 0
    else
        log "No secrets need rotation at this time"
        return 1
    fi
}

# Trigger secret rotation
trigger_rotation() {
    log "Triggering secret rotation process..."
    
    local response=$(curl -s -X POST \
        "${API_BASE_URL}/api/secrets/rotate" \
        -H "Authorization: Bearer ${SUPABASE_SERVICE_ROLE_KEY}" \
        -H "Content-Type: application/json" \
        -d '{}')
    
    if [[ $? -ne 0 ]]; then
        error "Failed to trigger secret rotation"
        return 1
    fi
    
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [[ "$success" == "true" ]]; then
        local rotated=$(echo "$response" | jq -r '.data.rotated // 0')
        local failed=$(echo "$response" | jq -r '.data.failed // 0')
        
        log "Secret rotation completed: $rotated rotated, $failed failed"
        
        if [[ "$failed" -gt 0 ]]; then
            warn "Some secrets failed to rotate. Check logs for details."
            echo "$response" | jq -r '.data.results[] | select(.status == "failed")'
        fi
        
        return 0
    else
        error "Secret rotation failed"
        echo "$response" | jq -r '.error // "Unknown error"'
        return 1
    fi
}

# Send notification about rotation status
send_notification() {
    local status="$1"
    local message="$2"
    
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local emoji="✅"
        if [[ "$status" == "error" ]]; then
            emoji="❌"
        elif [[ "$status" == "warning" ]]; then
            emoji="⚠️"
        fi
        
        local payload=$(jq -n \
            --arg text "$emoji Secret Rotation: $message" \
            '{text: $text}')
        
        curl -s -X POST "$SLACK_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "$payload" > /dev/null
        
        log "Notification sent to Slack"
    fi
}

# Main execution
main() {
    log "Starting secret rotation check..."
    
    # Check environment
    check_env
    
    # Check if rotation is needed
    if check_rotation_needed; then
        # Trigger rotation
        if trigger_rotation; then
            send_notification "success" "Secrets rotated successfully"
        else
            send_notification "error" "Secret rotation failed"
            exit 1
        fi
    else
        log "No action needed"
        send_notification "info" "No secrets need rotation"
    fi
    
    log "Secret rotation check completed"
}

# Handle script arguments
case "${1:-}" in
    "check")
        check_rotation_needed
        ;;
    "rotate")
        trigger_rotation
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [check|rotate]"
        echo "  check  - Only check which secrets need rotation"
        echo "  rotate - Force rotation of all eligible secrets"
        echo "  (no args) - Check and rotate if needed"
        exit 1
        ;;
esac
