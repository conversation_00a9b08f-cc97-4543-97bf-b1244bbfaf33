import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SecretRotationRequest {
  secretId?: string;
  keyType?: string;
  force?: boolean;
}

interface SlackWebhookPayload {
  text: string;
  blocks?: any[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Check for secrets that need rotation
    const { data: secretsToRotate, error: checkError } = await supabaseClient
      .rpc('check_secrets_for_rotation')

    if (checkError) {
      console.error('Error checking secrets for rotation:', checkError)
      throw checkError
    }

    const rotationResults = []

    for (const secret of secretsToRotate || []) {
      try {
        // Generate new API key based on type
        const newKey = await generateNewApiKey(secret.key_name, secret.key_type)
        
        if (newKey) {
          // Encrypt the new key
          const encryptedNewKey = await encryptSecret(newKey)
          
          // Get old key hash for audit
          const { data: oldSecret } = await supabaseClient
            .from('secrets')
            .select('encrypted_value')
            .eq('id', secret.secret_id)
            .single()

          const oldKeyHash = await hashSecret(oldSecret?.encrypted_value || '')
          const newKeyHash = await hashSecret(encryptedNewKey)

          // Update the secret
          const { error: updateError } = await supabaseClient
            .from('secrets')
            .update({
              encrypted_value: encryptedNewKey,
              updated_at: new Date().toISOString()
            })
            .eq('id', secret.secret_id)

          if (updateError) {
            console.error('Error updating secret:', updateError)
            continue
          }

          // Log the rotation
          await supabaseClient.rpc('log_secret_rotation', {
            p_secret_id: secret.secret_id,
            p_old_key_hash: oldKeyHash,
            p_new_key_hash: newKeyHash,
            p_reason: `Automatic rotation - ${secret.days_old} days old`
          })

          rotationResults.push({
            secretId: secret.secret_id,
            keyName: secret.key_name,
            daysOld: secret.days_old,
            status: 'rotated'
          })

          // Send Slack notification
          await sendSlackNotification({
            text: `🔄 Secret Rotated: ${secret.key_name}`,
            blocks: [
              {
                type: "section",
                text: {
                  type: "mrkdwn",
                  text: `*Secret Rotation Alert*\n\n• *Key:* ${secret.key_name}\n• *Age:* ${secret.days_old} days\n• *Status:* ✅ Successfully rotated\n• *Time:* ${new Date().toISOString()}`
                }
              }
            ]
          })
        }
      } catch (error) {
        console.error(`Error rotating secret ${secret.key_name}:`, error)
        rotationResults.push({
          secretId: secret.secret_id,
          keyName: secret.key_name,
          daysOld: secret.days_old,
          status: 'failed',
          error: error.message
        })

        // Send failure notification
        await sendSlackNotification({
          text: `❌ Secret Rotation Failed: ${secret.key_name} - ${error.message}`
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        rotated: rotationResults.filter(r => r.status === 'rotated').length,
        failed: rotationResults.filter(r => r.status === 'failed').length,
        results: rotationResults
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Secret rotation error:', error)
    
    // Send critical failure notification
    await sendSlackNotification({
      text: `🚨 Critical: Secret rotation system failure - ${error.message}`
    })

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

async function generateNewApiKey(keyName: string, keyType: string): Promise<string | null> {
  // This is a placeholder - in production, you would integrate with each provider's API
  // to generate new keys programmatically
  console.log(`Would generate new ${keyType} key for ${keyName}`)
  
  // For now, return null to indicate manual rotation needed
  return null
}

async function encryptSecret(secret: string): Promise<string> {
  // Simple base64 encoding - in production, use proper encryption
  return btoa(secret)
}

async function hashSecret(secret: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(secret)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

async function sendSlackNotification(payload: SlackWebhookPayload): Promise<void> {
  const webhookUrl = Deno.env.get('SLACK_WEBHOOK_URL')
  
  if (!webhookUrl) {
    console.log('No Slack webhook URL configured, skipping notification')
    return
  }

  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      console.error('Failed to send Slack notification:', response.statusText)
    }
  } catch (error) {
    console.error('Error sending Slack notification:', error)
  }
}
