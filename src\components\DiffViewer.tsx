
interface DiffViewerProps {
  diffContent: string;
}

export const DiffViewer = ({ diffContent }: DiffViewerProps) => {
  if (!diffContent) {
    return (
      <div className="h-full bg-slate-900 flex items-center justify-center">
        <div className="text-center text-slate-500">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-800 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-slate-600 rounded-sm flex items-center justify-center">
              <div className="w-4 h-4 bg-slate-600 rounded-sm"></div>
            </div>
          </div>
          <p className="text-lg font-medium">No diff available</p>
          <p className="text-sm">Run the loop to see code changes</p>
        </div>
      </div>
    );
  }

  const lines = diffContent.split('\n');

  return (
    <div className="h-full bg-slate-900 overflow-auto">
      <div className="p-4 font-mono text-sm">
        <div className="space-y-0">
          {lines.map((line, index) => {
            let className = "block px-3 py-1 border-l-2 ";
            let lineNumber = "";
            
            if (line.startsWith('---') || line.startsWith('+++')) {
              className += "text-slate-400 font-bold bg-slate-800/50 border-l-slate-600";
            } else if (line.startsWith('@@')) {
              className += "text-blue-400 bg-blue-900/20 border-l-blue-500";
              lineNumber = "@@";
            } else if (line.startsWith('+')) {
              className += "text-green-400 bg-green-900/20 border-l-green-500";
              lineNumber = "+";
            } else if (line.startsWith('-')) {
              className += "text-red-400 bg-red-900/20 border-l-red-500";
              lineNumber = "-";
            } else {
              className += "text-slate-300 border-l-slate-700";
              lineNumber = " ";
            }

            return (
              <div key={index} className={className}>
                <span className="inline-block w-6 text-center text-xs opacity-60 mr-2">
                  {lineNumber}
                </span>
                <span className="whitespace-pre-wrap">
                  {line.replace(/^[+\-\s]/, '') || ' '}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
