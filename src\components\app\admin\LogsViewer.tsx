import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  Download,
  AlertCircle,
  Info,
  AlertTriangle,
  XCircle,
  Bug,
  Calendar,
  Clock,
  User,
  Server
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface LogEntry {
  id: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  service: string;
  user_id?: string;
  session_id?: string;
  request_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

interface LogsViewerProps {
  adminMode?: boolean;
  maxHeight?: string;
}

export const LogsViewer = ({ adminMode = false, maxHeight = '600px' }: LogsViewerProps) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('');
  const [limit, setLimit] = useState(100);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const { toast } = useToast();

  const fetchLogs = async () => {
    try {
      setError(null);
      const params = new URLSearchParams();
      
      if (selectedService) params.append('service', selectedService);
      if (selectedLevel) params.append('level', selectedLevel);
      params.append('limit', limit.toString());

      const endpoint = searchQuery.trim() 
        ? `/api/logs/search?query=${encodeURIComponent(searchQuery)}&${params.toString()}`
        : `/api/logs?${params.toString()}`;

      const response = await fetch(endpoint, {
        headers: adminMode ? {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        } : undefined,
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch logs');
      }
      
      setLogs(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Logs Loading Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setLoading(true);
    fetchLogs();
  };

  const handleRefresh = () => {
    setLoading(true);
    fetchLogs();
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (selectedService) params.append('service', selectedService);
      if (selectedLevel) params.append('level', selectedLevel);
      if (searchQuery) params.append('query', searchQuery);
      params.append('limit', '1000'); // Export more logs
      
      const response = await fetch(`/api/logs?${params.toString()}`, {
        headers: adminMode ? {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        } : undefined,
      });
      
      if (!response.ok) throw new Error('Export failed');
      
      const result = await response.json();
      const blob = new Blob([JSON.stringify(result.data, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `logs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Export Complete',
        description: 'Logs have been downloaded as JSON file.',
      });
    } catch (err) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export logs.',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [selectedService, selectedLevel, limit]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchLogs, 5000);
    return () => clearInterval(interval);
  }, [autoRefresh, selectedService, selectedLevel, limit]);

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error':
        return <XCircle className="h-4 w-4 text-destructive" />;
      case 'warn':
        return <AlertTriangle className="h-4 w-4 text-warning" />;
      case 'info':
        return <Info className="h-4 w-4 text-info" />;
      case 'debug':
        return <Bug className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getLevelBadge = (level: LogEntry['level']) => {
    const variants = {
      error: 'bg-destructive/10 text-destructive-foreground border-destructive/20',
      warn: 'bg-warning/10 text-warning-foreground border-warning/20',
      info: 'bg-info/10 text-info-foreground border-info/20',
      debug: 'bg-muted/10 text-muted-foreground border-muted/20'
    };

    return (
      <Badge variant="outline" className={variants[level]}>
        {getLevelIcon(level)}
        <span className="ml-1 capitalize">{level}</span>
      </Badge>
    );
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  const uniqueServices = [...new Set(logs.map(log => log.service))];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              System Logs
              {adminMode && (
                <Badge variant="outline" className="bg-warning/10 text-warning-foreground">
                  Admin
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Real-time application logs and system events
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={logs.length === 0}
            >
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search">Search</Label>
            <div className="flex gap-2">
              <Input
                id="search"
                placeholder="Search logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button size="sm" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Service</Label>
            <Select value={selectedService} onValueChange={setSelectedService}>
              <SelectTrigger>
                <SelectValue placeholder="All services" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All services</SelectItem>
                {uniqueServices.map(service => (
                  <SelectItem key={service} value={service}>
                    {service}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Level</Label>
            <Select value={selectedLevel} onValueChange={setSelectedLevel}>
              <SelectTrigger>
                <SelectValue placeholder="All levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All levels</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="warn">Warning</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="debug">Debug</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Limit</Label>
            <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="50">50 logs</SelectItem>
                <SelectItem value="100">100 logs</SelectItem>
                <SelectItem value="250">250 logs</SelectItem>
                <SelectItem value="500">500 logs</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Auto-refresh toggle */}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="autoRefresh"
            checked={autoRefresh}
            onChange={(e) => setAutoRefresh(e.target.checked)}
            className="rounded"
          />
          <Label htmlFor="autoRefresh" className="text-sm">
            Auto-refresh every 5 seconds
          </Label>
        </div>

        {/* Logs Display */}
        {loading && logs.length === 0 ? (
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : logs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Server className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No logs found</p>
            <p className="text-sm">Try adjusting your filters or search query</p>
          </div>
        ) : (
          <ScrollArea style={{ height: maxHeight }}>
            <div className="space-y-2">
              {logs.map((log) => {
                const timestamp = formatTimestamp(log.created_at);
                return (
                  <div
                    key={log.id}
                    className="border rounded-lg p-3 bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          {getLevelBadge(log.level)}
                          <Badge variant="outline" className="text-xs">
                            {log.service}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            {timestamp.date}
                            <Clock className="h-3 w-3 ml-2" />
                            {timestamp.time}
                          </div>
                        </div>
                        
                        <p className="text-sm font-medium mb-1">{log.message}</p>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {log.user_id && (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {log.user_id.slice(0, 8)}...
                            </div>
                          )}
                          {log.session_id && (
                            <div>Session: {log.session_id.slice(0, 8)}...</div>
                          )}
                          {log.request_id && (
                            <div>Request: {log.request_id}</div>
                          )}
                        </div>
                        
                        {log.metadata && Object.keys(log.metadata).length > 0 && (
                          <details className="mt-2">
                            <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                              View metadata
                            </summary>
                            <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                              {JSON.stringify(log.metadata, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}

        {logs.length > 0 && (
          <div className="text-xs text-muted-foreground text-center">
            Showing {logs.length} log entries
          </div>
        )}
      </CardContent>
    </Card>
  );
};
