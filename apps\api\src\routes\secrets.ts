import { Router } from 'express';
import { z } from 'zod';
import { secretsService } from '../services/secretsService.js';

const router = Router();

// Request validation schemas
const storeSecretSchema = z.object({
  keyName: z.string().min(1, 'Key name is required'),
  value: z.string().min(1, 'Value is required'),
  keyType: z.enum(['openai', 'anthropic', 'github', 'slack', 'stripe'])
});

const updateSecretSchema = z.object({
  keyName: z.string().min(1, 'Key name is required'),
  value: z.string().min(1, 'Value is required')
});

// Middleware to check if user is admin (for now, just check for service role)
const requireAdmin = (req: any, res: any, next: any) => {
  // In production, implement proper admin authentication
  const authHeader = req.headers.authorization;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!authHeader || !authHeader.includes(serviceRoleKey)) {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }
  
  next();
};

// POST /api/secrets - Store a new secret
router.post('/secrets', requireAdmin, async (req, res) => {
  try {
    const validatedRequest = storeSecretSchema.parse(req.body);
    
    const secret = await secretsService.storeSecret(
      validatedRequest.keyName,
      validatedRequest.value,
      validatedRequest.keyType
    );
    
    res.json({
      success: true,
      data: {
        id: secret.id,
        keyName: secret.key_name,
        keyType: secret.key_type,
        createdAt: secret.created_at
      }
    });
  } catch (error) {
    console.error('Store secret error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to store secret',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/secrets - List all secrets (without values)
router.get('/secrets', requireAdmin, async (req, res) => {
  try {
    const secrets = await secretsService.listSecrets();
    
    res.json({
      success: true,
      data: secrets.map(secret => ({
        id: secret.id,
        keyName: secret.key_name,
        keyType: secret.key_type,
        createdAt: secret.created_at,
        updatedAt: secret.updated_at,
        rotationCount: secret.rotation_count,
        lastRotatedAt: secret.last_rotated_at,
        isActive: secret.is_active
      }))
    });
  } catch (error) {
    console.error('List secrets error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to list secrets',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/secrets/:keyName - Update a secret value
router.put('/secrets/:keyName', requireAdmin, async (req, res) => {
  try {
    const keyName = req.params.keyName;
    const { value } = updateSecretSchema.parse({ keyName, ...req.body });
    
    await secretsService.updateSecret(keyName, value);
    
    res.json({
      success: true,
      message: 'Secret updated successfully'
    });
  } catch (error) {
    console.error('Update secret error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to update secret',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/secrets/:keyName - Delete a secret
router.delete('/secrets/:keyName', requireAdmin, async (req, res) => {
  try {
    const keyName = req.params.keyName;
    
    await secretsService.deleteSecret(keyName);
    
    res.json({
      success: true,
      message: 'Secret deleted successfully'
    });
  } catch (error) {
    console.error('Delete secret error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to delete secret',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/secrets/rotation-check - Check which secrets need rotation
router.get('/secrets/rotation-check', requireAdmin, async (req, res) => {
  try {
    const secretsForRotation = await secretsService.getSecretsForRotation();
    
    res.json({
      success: true,
      data: secretsForRotation,
      count: secretsForRotation.length
    });
  } catch (error) {
    console.error('Rotation check error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to check secrets for rotation',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/secrets/rotate - Trigger secret rotation
router.post('/secrets/rotate', requireAdmin, async (req, res) => {
  try {
    // Call the Supabase Edge Function for rotation
    const supabaseUrl = process.env.SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    const response = await fetch(`${supabaseUrl}/functions/v1/secret-rotation`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${serviceRoleKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(req.body)
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Rotation failed');
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Secret rotation error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to rotate secrets',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/secrets/initialize - Initialize secrets from environment variables
router.post('/secrets/initialize', requireAdmin, async (req, res) => {
  try {
    await secretsService.initializeFromEnv();
    
    res.json({
      success: true,
      message: 'Secrets initialized from environment variables'
    });
  } catch (error) {
    console.error('Initialize secrets error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to initialize secrets',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as secretsRouter };
