name: Secret Age Check

on:
  schedule:
    # Run nightly at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual trigger

jobs:
  check-secret-age:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Check secret ages
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: |
          node -e "
          const { createClient } = require('@supabase/supabase-js');
          
          async function checkSecretAges() {
            const supabase = createClient(
              process.env.SUPABASE_URL,
              process.env.SUPABASE_SERVICE_ROLE_KEY
            );
            
            try {
              // Query secrets with their creation/update dates
              const { data: secrets, error } = await supabase
                .from('encrypted_secrets')
                .select('key_name, created_at, updated_at')
                .order('updated_at', { ascending: true });
                
              if (error) {
                console.error('Error fetching secrets:', error);
                process.exit(1);
              }
              
              const now = new Date();
              const ninetyDaysAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
              const oldSecrets = [];
              
              for (const secret of secrets) {
                const lastUpdated = new Date(secret.updated_at || secret.created_at);
                if (lastUpdated < ninetyDaysAgo) {
                  const ageInDays = Math.floor((now - lastUpdated) / (24 * 60 * 60 * 1000));
                  oldSecrets.push({
                    name: secret.key_name,
                    age: ageInDays,
                    lastUpdated: lastUpdated.toISOString().split('T')[0]
                  });
                }
              }
              
              if (oldSecrets.length > 0) {
                const message = {
                  text: '🔐 Secret Rotation Alert',
                  blocks: [
                    {
                      type: 'header',
                      text: {
                        type: 'plain_text',
                        text: '🔐 Secret Rotation Required'
                      }
                    },
                    {
                      type: 'section',
                      text: {
                        type: 'mrkdwn',
                        text: \`The following secrets are older than 90 days and should be rotated:\`
                      }
                    },
                    {
                      type: 'section',
                      fields: oldSecrets.map(secret => ({
                        type: 'mrkdwn',
                        text: \`*\${secret.name}*\\n\${secret.age} days old (last updated: \${secret.lastUpdated})\`
                      }))
                    },
                    {
                      type: 'section',
                      text: {
                        type: 'mrkdwn',
                        text: '⚠️ Please rotate these secrets as soon as possible for security compliance.'
                      }
                    }
                  ]
                };
                
                // Send Slack notification
                if (process.env.SLACK_WEBHOOK_URL) {
                  const response = await fetch(process.env.SLACK_WEBHOOK_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(message)
                  });
                  
                  if (response.ok) {
                    console.log('Slack notification sent successfully');
                  } else {
                    console.error('Failed to send Slack notification:', response.statusText);
                  }
                } else {
                  console.log('No Slack webhook configured, logging alert:');
                  console.log(JSON.stringify(message, null, 2));
                }
                
                // Exit with error to mark workflow as failed
                process.exit(1);
              } else {
                console.log('✅ All secrets are within the 90-day rotation window');
              }
            } catch (error) {
              console.error('Error checking secret ages:', error);
              process.exit(1);
            }
          }
          
          checkSecretAges();
          "
          
      - name: Log completion
        if: success()
        run: echo "✅ Secret age check completed successfully - no rotation needed"
        
      - name: Log failure
        if: failure()
        run: echo "❌ Secret age check failed - rotation required or error occurred"
