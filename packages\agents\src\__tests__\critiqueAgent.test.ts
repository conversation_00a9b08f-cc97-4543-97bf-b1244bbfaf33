import { CritiqueAgent } from '../critiqueAgent';
import { CritiqueRequest, JSONPatch, CritiqueResult } from '../types';

describe('CritiqueAgent', () => {
  let critiqueAgent: CritiqueAgent;

  beforeEach(() => {
    critiqueAgent = new CritiqueAgent({
      model: 'gemini-2.5',
      temperature: 0.3,
      maxTokens: 1500
    });
  });

  describe('scorePatch', () => {
    it('should score a high-quality patch highly', async () => {
      const highQualityPatch: JSONPatch = {
        operations: [
          { op: 'add', path: '/errorHandling', value: { try: true, catch: true } },
          { op: 'replace', path: '/validation', value: 'comprehensive' }
        ],
        description: 'Comprehensive error handling and validation improvements',
        confidence: 0.9
      };

      const request: CritiqueRequest = {
        patch: highQualityPatch,
        originalPrompt: 'Add robust error handling'
      };

      const result = await critiqueAgent.scorePatch(request);

      expect(result.score).toBeGreaterThan(0.7);
      expect(result.feedback).toBeTruthy();
      expect(result.suggestions).toBeInstanceOf(Array);
      expect(typeof result.isAcceptable).toBe('boolean');
    });

    it('should score a low-quality patch poorly', async () => {
      const lowQualityPatch: JSONPatch = {
        operations: [
          { op: 'add', path: '/temp', value: 'quick fix' }
        ],
        description: 'Quick fix',
        confidence: 0.3
      };

      const request: CritiqueRequest = {
        patch: lowQualityPatch,
        originalPrompt: 'Implement comprehensive solution'
      };

      const result = await critiqueAgent.scorePatch(request);

      expect(result.score).toBeLessThan(0.6);
      expect(result.isAcceptable).toBe(false);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should mark patches with score >= 0.95 as acceptable', async () => {
      // Mock a high-scoring patch by manipulating the scoring logic
      const excellentPatch: JSONPatch = {
        operations: [
          { op: 'add', path: '/feature', value: 'well-implemented' },
          { op: 'add', path: '/tests', value: 'comprehensive' },
          { op: 'add', path: '/documentation', value: 'detailed' }
        ],
        description: 'Excellent implementation with tests and documentation',
        confidence: 0.95
      };

      const request: CritiqueRequest = {
        patch: excellentPatch,
        originalPrompt: 'Implement feature with best practices'
      };

      const result = await critiqueAgent.scorePatch(request);

      // The mock implementation should give this a high score
      if (result.score >= 0.95) {
        expect(result.isAcceptable).toBe(true);
      }
    });

    it('should include context in critique evaluation', async () => {
      const patch: JSONPatch = {
        operations: [{ op: 'add', path: '/feature', value: 'implementation' }],
        description: 'Feature implementation',
        confidence: 0.7
      };

      const request: CritiqueRequest = {
        patch,
        originalPrompt: 'Add new feature',
        context: { framework: 'react', language: 'typescript' }
      };

      const result = await critiqueAgent.scorePatch(request);

      expect(result).toBeDefined();
      expect(result.feedback).toBeTruthy();
    });

    it('should handle empty patches', async () => {
      const emptyPatch: JSONPatch = {
        operations: [],
        description: 'Empty patch',
        confidence: 0.1
      };

      const request: CritiqueRequest = {
        patch: emptyPatch,
        originalPrompt: 'Make improvements'
      };

      const result = await critiqueAgent.scorePatch(request);

      expect(result.score).toBeLessThan(0.5);
      expect(result.isAcceptable).toBe(false);
    });
  });

  describe('validateCritique', () => {
    it('should validate a correct critique result', async () => {
      const validCritique: CritiqueResult = {
        score: 0.8,
        feedback: 'Good implementation with minor issues',
        suggestions: ['Add error handling', 'Improve documentation'],
        isAcceptable: false
      };

      const isValid = await critiqueAgent.validateCritique(validCritique);
      expect(isValid).toBe(true);
    });

    it('should reject critique with invalid score', async () => {
      const invalidCritique: CritiqueResult = {
        score: 1.5,
        feedback: 'Invalid score',
        suggestions: [],
        isAcceptable: false
      };

      const isValid = await critiqueAgent.validateCritique(invalidCritique);
      expect(isValid).toBe(false);
    });

    it('should reject critique without feedback', async () => {
      const invalidCritique = {
        score: 0.8,
        suggestions: [],
        isAcceptable: false
      } as any;

      const isValid = await critiqueAgent.validateCritique(invalidCritique);
      expect(isValid).toBe(false);
    });

    it('should reject critique with non-array suggestions', async () => {
      const invalidCritique = {
        score: 0.8,
        feedback: 'Valid feedback',
        suggestions: 'not an array',
        isAcceptable: false
      } as any;

      const isValid = await critiqueAgent.validateCritique(invalidCritique);
      expect(isValid).toBe(false);
    });
  });

  describe('scoring logic', () => {
    it('should provide different scores for different patch qualities', async () => {
      const patches: JSONPatch[] = [
        {
          operations: [{ op: 'add', path: '/quick', value: 'fix' }],
          description: 'Quick fix',
          confidence: 0.3
        },
        {
          operations: [
            { op: 'add', path: '/proper', value: 'implementation' },
            { op: 'add', path: '/tests', value: 'included' }
          ],
          description: 'Proper implementation with tests',
          confidence: 0.8
        }
      ];

      const scores = [];
      for (const patch of patches) {
        const request: CritiqueRequest = {
          patch,
          originalPrompt: 'Implement feature'
        };
        const result = await critiqueAgent.scorePatch(request);
        scores.push(result.score);
      }

      expect(scores[1]).toBeGreaterThan(scores[0]);
    });

    it('should provide appropriate feedback for different score ranges', async () => {
      const lowQualityPatch: JSONPatch = {
        operations: [{ op: 'add', path: '/temp', value: 'fix' }],
        description: 'Temporary fix',
        confidence: 0.2
      };

      const request: CritiqueRequest = {
        patch: lowQualityPatch,
        originalPrompt: 'Implement robust solution'
      };

      const result = await critiqueAgent.scorePatch(request);

      if (result.score < 0.4) {
        expect(result.feedback).toContain('Poor');
      } else if (result.score < 0.6) {
        expect(result.feedback).toContain('significant issues');
      }
    });
  });
});
