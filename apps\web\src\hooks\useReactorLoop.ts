import { useState, useCallback, useRef } from 'react';
import { reactor<PERSON>pi, LoopProgress, LoopResult } from '../services/reactorApi';

interface StreamEvent {
  id: string;
  timestamp: Date;
  type: 'plan' | 'critique' | 'iteration' | 'complete' | 'error';
  content: string;
  score?: number;
  iteration?: number;
}

interface UseReactorLoopReturn {
  isRunning: boolean;
  events: StreamEvent[];
  currentPatch: any;
  finalResult: LoopResult | null;
  progress: number;
  runLoop: (prompt: string, maxLoops?: number) => Promise<void>;
  stopLoop: () => void;
  clearEvents: () => void;
}

export const useReactorLoop = (): UseReactorLoopReturn => {
  const [isRunning, setIsRunning] = useState(false);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [currentPatch, setCurrentPatch] = useState<any>(null);
  const [finalResult, setFinalResult] = useState<LoopResult | null>(null);
  const [progress, setProgress] = useState(0);
  const abortControllerRef = useRef<AbortController | null>(null);

  const addEvent = useCallback((event: Omit<StreamEvent, 'id' | 'timestamp'>) => {
    const newEvent: StreamEvent = {
      ...event,
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date()
    };
    setEvents(prev => [...prev, newEvent]);
  }, []);

  const runLoop = useCallback(async (prompt: string, maxLoops: number = 10) => {
    if (isRunning) return;

    setIsRunning(true);
    setEvents([]);
    setCurrentPatch(null);
    setFinalResult(null);
    setProgress(0);

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    addEvent({
      type: 'iteration',
      content: `🚀 Starting Metamorphic Reactor loop with prompt: "${prompt}"`,
      iteration: 0
    });

    try {
      await reactorApi.runStreamingLoop(
        { prompt, maxLoops },
        (progressData: LoopProgress) => {
          // Handle progress updates
          addEvent({
            type: 'plan',
            content: progressData.plan,
            iteration: progressData.iteration
          });

          addEvent({
            type: 'critique',
            content: progressData.critique,
            score: progressData.score,
            iteration: progressData.iteration
          });

          addEvent({
            type: 'iteration',
            content: `Iteration ${progressData.iteration} completed with score: ${(progressData.score * 100).toFixed(1)}%`,
            score: progressData.score,
            iteration: progressData.iteration
          });

          setCurrentPatch(progressData.patch);
          setProgress((progressData.iteration / maxLoops) * 100);

          if (progressData.isComplete) {
            addEvent({
              type: 'complete',
              content: `🎉 Target score achieved! Loop completed successfully.`,
              score: progressData.score,
              iteration: progressData.iteration
            });
          }
        },
        (result: LoopResult) => {
          // Handle completion
          setFinalResult(result);
          setProgress(100);
          
          addEvent({
            type: 'complete',
            content: `✅ Reactor loop completed! Final score: ${(result.score * 100).toFixed(1)}% after ${result.iterations} iterations.`
          });
        },
        (error: string) => {
          // Handle errors
          addEvent({
            type: 'error',
            content: `❌ Error: ${error}`
          });
          setProgress(0);
        }
      );
    } catch (error) {
      addEvent({
        type: 'error',
        content: `❌ Failed to start reactor loop: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      setProgress(0);
    } finally {
      setIsRunning(false);
      abortControllerRef.current = null;
    }
  }, [isRunning, addEvent]);

  const stopLoop = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setIsRunning(false);
    setProgress(0);
    
    addEvent({
      type: 'error',
      content: '⏹️ Reactor loop stopped by user'
    });
  }, [addEvent]);

  const clearEvents = useCallback(() => {
    setEvents([]);
    setCurrentPatch(null);
    setFinalResult(null);
    setProgress(0);
  }, []);

  return {
    isRunning,
    events,
    currentPatch,
    finalResult,
    progress,
    runLoop,
    stopLoop,
    clearEvents
  };
};
