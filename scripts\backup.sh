#!/bin/bash

# Database Backup Script for Metamorphic Reactor
# Performs manual pg_dump backups to S3-compatible storage

set -e

# Configuration
SUPABASE_DB_URL="${SUPABASE_DB_URL:-}"
SUPABASE_PROJECT_REF="${SUPABASE_PROJECT_REF:-hbdxefluyzayeyhokinw}"
BACKUP_S3_BUCKET="${BACKUP_S3_BUCKET:-metamorphic-reactor-backups}"
BACKUP_S3_REGION="${BACKUP_S3_REGION:-us-east-2}"
AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID:-}"
AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY:-}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    local missing_deps=()
    
    if ! command -v pg_dump &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if ! command -v aws &> /dev/null; then
        missing_deps+=("awscli")
    fi
    
    if ! command -v gzip &> /dev/null; then
        missing_deps+=("gzip")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        error "Missing required dependencies: ${missing_deps[*]}"
        error "Please install them before running this script"
        exit 1
    fi
    
    log "All dependencies are available"
}

# Check if required environment variables are set
check_env() {
    local missing_vars=()
    
    if [[ -z "$SUPABASE_DB_URL" ]]; then
        missing_vars+=("SUPABASE_DB_URL")
    fi
    
    if [[ -z "$AWS_ACCESS_KEY_ID" ]]; then
        missing_vars+=("AWS_ACCESS_KEY_ID")
    fi
    
    if [[ -z "$AWS_SECRET_ACCESS_KEY" ]]; then
        missing_vars+=("AWS_SECRET_ACCESS_KEY")
    fi
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "Missing required environment variables: ${missing_vars[*]}"
        exit 1
    fi
    
    log "Environment variables validated"
}

# Create backup filename with timestamp
generate_backup_filename() {
    local timestamp=$(date +'%Y%m%d_%H%M%S')
    echo "metamorphic_reactor_${SUPABASE_PROJECT_REF}_${timestamp}.sql.gz"
}

# Perform database backup
perform_backup() {
    local backup_file="$1"
    local temp_file="/tmp/$(basename "$backup_file" .gz)"
    
    log "Starting database backup..."
    info "Backup file: $backup_file"
    
    # Perform pg_dump
    if pg_dump "$SUPABASE_DB_URL" \
        --verbose \
        --no-owner \
        --no-privileges \
        --format=plain \
        --file="$temp_file"; then
        
        log "Database dump completed successfully"
    else
        error "Database dump failed"
        rm -f "$temp_file"
        return 1
    fi
    
    # Compress the backup
    log "Compressing backup file..."
    if gzip "$temp_file"; then
        log "Backup compressed successfully"
        mv "${temp_file}.gz" "/tmp/$backup_file"
    else
        error "Failed to compress backup"
        rm -f "$temp_file"
        return 1
    fi
    
    return 0
}

# Upload backup to S3
upload_to_s3() {
    local backup_file="$1"
    local local_path="/tmp/$backup_file"
    local s3_path="s3://${BACKUP_S3_BUCKET}/backups/$backup_file"
    
    log "Uploading backup to S3..."
    info "S3 destination: $s3_path"
    
    # Configure AWS CLI
    aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID"
    aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY"
    aws configure set default.region "$BACKUP_S3_REGION"
    
    # Create bucket if it doesn't exist
    if ! aws s3 ls "s3://${BACKUP_S3_BUCKET}" &> /dev/null; then
        log "Creating S3 bucket: $BACKUP_S3_BUCKET"
        aws s3 mb "s3://${BACKUP_S3_BUCKET}" --region "$BACKUP_S3_REGION"
    fi
    
    # Upload the backup
    if aws s3 cp "$local_path" "$s3_path" \
        --storage-class STANDARD_IA \
        --metadata "project=${SUPABASE_PROJECT_REF},backup-type=manual,created-at=$(date -u +%Y-%m-%dT%H:%M:%SZ)"; then
        
        log "Backup uploaded successfully to S3"
        
        # Clean up local file
        rm -f "$local_path"
        
        return 0
    else
        error "Failed to upload backup to S3"
        return 1
    fi
}

# Clean up old backups
cleanup_old_backups() {
    log "Cleaning up backups older than $BACKUP_RETENTION_DAYS days..."
    
    local cutoff_date=$(date -d "$BACKUP_RETENTION_DAYS days ago" +%Y-%m-%d)
    
    # List and delete old backups
    aws s3 ls "s3://${BACKUP_S3_BUCKET}/backups/" --recursive | while read -r line; do
        local file_date=$(echo "$line" | awk '{print $1}')
        local file_name=$(echo "$line" | awk '{print $4}')
        
        if [[ "$file_date" < "$cutoff_date" ]]; then
            log "Deleting old backup: $file_name"
            aws s3 rm "s3://${BACKUP_S3_BUCKET}/$file_name"
        fi
    done
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    local local_path="/tmp/$backup_file"
    
    log "Verifying backup integrity..."
    
    # Download backup from S3 for verification
    aws s3 cp "s3://${BACKUP_S3_BUCKET}/backups/$backup_file" "$local_path"
    
    # Test decompression
    if gzip -t "$local_path"; then
        log "Backup compression integrity verified"
    else
        error "Backup compression is corrupted"
        rm -f "$local_path"
        return 1
    fi
    
    # Test SQL syntax (basic check)
    if zcat "$local_path" | head -100 | grep -q "PostgreSQL database dump"; then
        log "Backup appears to be a valid PostgreSQL dump"
    else
        warn "Backup may not be a valid PostgreSQL dump"
    fi
    
    # Clean up verification file
    rm -f "$local_path"
    
    return 0
}

# List available backups
list_backups() {
    log "Available backups in S3:"
    
    aws s3 ls "s3://${BACKUP_S3_BUCKET}/backups/" --recursive --human-readable | \
    awk '{print $1" "$2" "$3" "$4}' | \
    sort -r | \
    head -20
}

# Restore from backup
restore_backup() {
    local backup_file="$1"
    local local_path="/tmp/$backup_file"
    
    if [[ -z "$backup_file" ]]; then
        error "Backup filename is required for restore"
        return 1
    fi
    
    warn "This will restore the database from backup: $backup_file"
    warn "This operation will OVERWRITE the current database!"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        log "Restore cancelled"
        return 0
    fi
    
    log "Downloading backup from S3..."
    aws s3 cp "s3://${BACKUP_S3_BUCKET}/backups/$backup_file" "$local_path"
    
    log "Restoring database from backup..."
    if zcat "$local_path" | psql "$SUPABASE_DB_URL"; then
        log "Database restored successfully"
    else
        error "Database restore failed"
        rm -f "$local_path"
        return 1
    fi
    
    # Clean up
    rm -f "$local_path"
    
    return 0
}

# Send notification about backup status
send_notification() {
    local status="$1"
    local message="$2"
    local backup_file="$3"
    
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local emoji="✅"
        if [[ "$status" == "error" ]]; then
            emoji="❌"
        elif [[ "$status" == "warning" ]]; then
            emoji="⚠️"
        fi
        
        local payload=$(jq -n \
            --arg text "$emoji Database Backup: $message" \
            --arg backup_file "$backup_file" \
            '{
                text: $text,
                blocks: [
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: "*Database Backup Status*\n\n• *Status:* \($text)\n• *File:* \($backup_file)\n• *Time:* \(now | strftime("%Y-%m-%d %H:%M:%S"))"
                        }
                    }
                ]
            }')
        
        curl -s -X POST "$SLACK_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "$payload" > /dev/null
        
        log "Notification sent to Slack"
    fi
}

# Main execution
main() {
    local backup_file=$(generate_backup_filename)
    
    log "Starting database backup process..."
    
    # Check dependencies and environment
    check_dependencies
    check_env
    
    # Perform backup
    if perform_backup "$backup_file"; then
        # Upload to S3
        if upload_to_s3 "$backup_file"; then
            # Verify backup
            if verify_backup "$backup_file"; then
                # Clean up old backups
                cleanup_old_backups
                
                send_notification "success" "Backup completed successfully" "$backup_file"
                log "Backup process completed successfully"
            else
                send_notification "warning" "Backup completed but verification failed" "$backup_file"
                warn "Backup completed but verification failed"
            fi
        else
            send_notification "error" "Backup upload failed" "$backup_file"
            error "Backup upload failed"
            exit 1
        fi
    else
        send_notification "error" "Backup creation failed" "$backup_file"
        error "Backup creation failed"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "backup"|"")
        main
        ;;
    "list")
        check_env
        list_backups
        ;;
    "restore")
        check_dependencies
        check_env
        restore_backup "$2"
        ;;
    "verify")
        check_dependencies
        check_env
        verify_backup "$2"
        ;;
    *)
        echo "Usage: $0 [backup|list|restore <filename>|verify <filename>]"
        echo "  backup   - Create and upload a new backup (default)"
        echo "  list     - List available backups in S3"
        echo "  restore  - Restore from a specific backup file"
        echo "  verify   - Verify integrity of a specific backup file"
        exit 1
        ;;
esac
