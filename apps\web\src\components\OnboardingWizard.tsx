import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Progress } from './ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { CheckCircle, Circle, Key, Github, Play, ArrowRight, ArrowLeft } from 'lucide-react';
import { useToast } from './ui/use-toast';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

interface OnboardingProgress {
  completed: boolean;
  current_step: number;
  steps: {
    api_keys: boolean;
    github_connection: boolean;
    sample_loop: boolean;
  };
  started_at: string;
  completed_at?: string;
}

interface OnboardingWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

export const OnboardingWizard: React.FC<OnboardingWizardProps> = ({
  isOpen,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [progress, setProgress] = useState<OnboardingProgress | null>(null);
  const [loading, setLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState({
    openai: '',
    anthropic: '',
    github: ''
  });
  const [sampleLoopRunning, setSampleLoopRunning] = useState(false);
  const { toast } = useToast();

  const steps: OnboardingStep[] = [
    {
      id: 'api_keys',
      title: 'Configure API Keys',
      description: 'Set up your AI provider API keys to enable code transformations',
      icon: <Key className="w-5 h-5" />,
      completed: progress?.steps.api_keys || false
    },
    {
      id: 'github_connection',
      title: 'Connect GitHub',
      description: 'Link your GitHub account to enable automatic PR creation',
      icon: <Github className="w-5 h-5" />,
      completed: progress?.steps.github_connection || false
    },
    {
      id: 'sample_loop',
      title: 'Run Sample Loop',
      description: 'Test the system with a simple code transformation',
      icon: <Play className="w-5 h-5" />,
      completed: progress?.steps.sample_loop || false
    }
  ];

  // Load onboarding progress on mount
  useEffect(() => {
    loadProgress();
  }, []);

  // Update current step based on progress
  useEffect(() => {
    if (progress) {
      setCurrentStep(progress.current_step);
    }
  }, [progress]);

  const loadProgress = async () => {
    try {
      const response = await fetch('/api/settings/onboarding-progress', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProgress(data.progress);
      }
    } catch (error) {
      console.error('Failed to load onboarding progress:', error);
    }
  };

  const updateProgress = async (step: string, completed: boolean = true) => {
    try {
      const response = await fetch('/api/settings/onboarding-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ step, completed })
      });

      if (response.ok) {
        const data = await response.json();
        setProgress(data.progress);
        
        if (data.progress.completed) {
          toast({
            title: "Onboarding Complete! 🎉",
            description: "You're all set up and ready to start transforming code!"
          });
          onComplete();
        }
      }
    } catch (error) {
      console.error('Failed to update progress:', error);
      toast({
        title: "Error",
        description: "Failed to save progress. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleApiKeysSubmit = async () => {
    setLoading(true);
    try {
      // Save API keys
      const response = await fetch('/api/settings/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(apiKeys)
      });

      if (response.ok) {
        await updateProgress('api_keys', true);
        toast({
          title: "API Keys Saved",
          description: "Your API keys have been securely stored."
        });
        setCurrentStep(2);
      } else {
        throw new Error('Failed to save API keys');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save API keys. Please check your keys and try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGitHubConnect = async () => {
    setLoading(true);
    try {
      // Initiate GitHub OAuth flow
      const response = await fetch('/api/auth/github/connect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        // Redirect to GitHub OAuth
        window.location.href = data.authUrl;
      } else {
        throw new Error('Failed to initiate GitHub connection');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to connect to GitHub. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSampleLoop = async () => {
    setSampleLoopRunning(true);
    try {
      const samplePrompt = "Create a simple React component that displays 'Hello, World!'";
      
      const response = await fetch('/api/queue/reactor/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          prompt: samplePrompt,
          context: { 
            framework: 'react',
            language: 'typescript',
            onboarding: true
          },
          maxLoops: 2,
          priority: 10 // High priority for onboarding
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        toast({
          title: "Sample Loop Started",
          description: `Your sample transformation is running (Session: ${data.sessionId})`
        });

        // Mark as completed after a short delay (simulating completion)
        setTimeout(async () => {
          await updateProgress('sample_loop', true);
          setSampleLoopRunning(false);
          toast({
            title: "Sample Loop Complete",
            description: "Great! Your first code transformation was successful."
          });
        }, 5000);
      } else {
        throw new Error('Failed to start sample loop');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to run sample loop. Please try again.",
        variant: "destructive"
      });
      setSampleLoopRunning(false);
    }
  };

  const getProgressPercentage = () => {
    if (!progress) return 0;
    const completedSteps = Object.values(progress.steps).filter(Boolean).length;
    return (completedSteps / 3) * 100;
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 1:
        return apiKeys.openai || apiKeys.anthropic;
      case 2:
        return progress?.steps.github_connection;
      case 3:
        return progress?.steps.sample_loop;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="openai-key">OpenAI API Key (Optional)</Label>
              <Input
                id="openai-key"
                type="password"
                placeholder="sk-..."
                value={apiKeys.openai}
                onChange={(e) => setApiKeys(prev => ({ ...prev, openai: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="anthropic-key">Anthropic API Key (Optional)</Label>
              <Input
                id="anthropic-key"
                type="password"
                placeholder="sk-ant-..."
                value={apiKeys.anthropic}
                onChange={(e) => setApiKeys(prev => ({ ...prev, anthropic: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="github-key">GitHub Token (Optional)</Label>
              <Input
                id="github-key"
                type="password"
                placeholder="ghp_..."
                value={apiKeys.github}
                onChange={(e) => setApiKeys(prev => ({ ...prev, github: e.target.value }))}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              You need at least one AI provider key (OpenAI or Anthropic) to continue.
            </p>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Connect your GitHub account to enable automatic pull request creation for your code transformations.
            </p>
            {progress?.steps.github_connection ? (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span>GitHub account connected successfully!</span>
              </div>
            ) : (
              <Button onClick={handleGitHubConnect} disabled={loading} className="w-full">
                <Github className="w-4 h-4 mr-2" />
                Connect GitHub Account
              </Button>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Let's run a simple code transformation to make sure everything is working correctly.
            </p>
            {progress?.steps.sample_loop ? (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span>Sample transformation completed successfully!</span>
              </div>
            ) : (
              <Button 
                onClick={handleSampleLoop} 
                disabled={sampleLoopRunning} 
                className="w-full"
              >
                <Play className="w-4 h-4 mr-2" />
                {sampleLoopRunning ? 'Running Sample Loop...' : 'Run Sample Loop'}
              </Button>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <span>Welcome to Metamorphic Reactor</span>
            <Badge variant="secondary">Setup Wizard</Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Setup Progress</span>
              <span>{Math.round(getProgressPercentage())}%</span>
            </div>
            <Progress value={getProgressPercentage()} className="w-full" />
          </div>

          {/* Steps Overview */}
          <div className="grid grid-cols-3 gap-4">
            {steps.map((step, index) => (
              <Card 
                key={step.id} 
                className={`cursor-pointer transition-colors ${
                  currentStep === index + 1 ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setCurrentStep(index + 1)}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-2">
                    {step.completed ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <Circle className="w-5 h-5 text-muted-foreground" />
                    )}
                    {step.icon}
                  </div>
                </CardHeader>
                <CardContent>
                  <CardTitle className="text-sm">{step.title}</CardTitle>
                  <CardDescription className="text-xs">
                    {step.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Current Step Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {steps[currentStep - 1]?.icon}
                <span>{steps[currentStep - 1]?.title}</span>
              </CardTitle>
              <CardDescription>
                {steps[currentStep - 1]?.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderStepContent()}
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="space-x-2">
              {currentStep < 3 ? (
                <Button
                  onClick={() => {
                    if (currentStep === 1) {
                      handleApiKeysSubmit();
                    } else {
                      setCurrentStep(currentStep + 1);
                    }
                  }}
                  disabled={!canProceedToNext() || loading}
                >
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={onClose}
                  disabled={!progress?.completed}
                >
                  Complete Setup
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
